using System;
using System.Linq;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.Text;
using System.Threading.Tasks;
using TradingPlatform.BusinessLayer;
using System.Net.Http;
using System.Text.Json;
using System.Threading;
using System.Security.Cryptography;
using System.Reflection;



namespace SelectedRangeLinesIndicator
{
    // 加密安全的随机数生成器类
    public class CryptoSecureRandom
    {
        private readonly RandomNumberGenerator rng;
        private readonly Random fallbackRandom;

        public CryptoSecureRandom()
        {
            rng = RandomNumberGenerator.Create();
            // 使用多种熵源创建种子
            int seed = GenerateEntropySeed();
            fallbackRandom = new Random(seed);
        }

        private int GenerateEntropySeed()
        {
            // 结合多种熵源生成种子
            int seed = Environment.TickCount;
            seed ^= DateTime.Now.Millisecond;
            seed ^= DateTime.Now.Ticks.GetHashCode();
            seed ^= Thread.CurrentThread.ManagedThreadId;
            seed ^= Environment.ProcessorCount;
            seed ^= GC.GetTotalMemory(false).GetHashCode();

            // 添加加密随机数作为额外熵源
            byte[] cryptoBytes = new byte[4];
            rng.GetBytes(cryptoBytes);
            seed ^= BitConverter.ToInt32(cryptoBytes, 0);

            return seed;
        }

        public int Next(int maxValue)
        {
            if (maxValue <= 0)
                throw new ArgumentOutOfRangeException(nameof(maxValue));

            // 使用加密安全的方法生成随机数
            byte[] randomBytes = new byte[4];
            rng.GetBytes(randomBytes);
            uint randomValue = BitConverter.ToUInt32(randomBytes, 0);

            // 避免模运算偏差的安全方法
            uint range = (uint)maxValue;
            uint maxValidValue = uint.MaxValue - (uint.MaxValue % range);

            // 如果随机值超出有效范围，重新生成
            while (randomValue >= maxValidValue)
            {
                rng.GetBytes(randomBytes);
                randomValue = BitConverter.ToUInt32(randomBytes, 0);
            }

            return (int)(randomValue % range);
        }

        public int Next(int minValue, int maxValue)
        {
            if (minValue >= maxValue)
                throw new ArgumentOutOfRangeException();

            return minValue + Next(maxValue - minValue);
        }

        public double NextDouble()
        {
            // 生成高质量的双精度随机数
            byte[] randomBytes = new byte[8];
            rng.GetBytes(randomBytes);
            ulong randomValue = BitConverter.ToUInt64(randomBytes, 0);

            // 转换为 [0, 1) 范围的双精度数
            return (randomValue >> 11) * (1.0 / (1UL << 53));
        }

        // 高级洗牌算法 - Fisher-Yates with crypto random
        public void Shuffle<T>(IList<T> list)
        {
            for (int i = list.Count - 1; i > 0; i--)
            {
                int j = Next(i + 1);
                T temp = list[i];
                list[i] = list[j];
                list[j] = temp;
            }
        }

        // 添加额外的熵源混合
        public void AddEntropy()
        {
            // 重新生成种子并更新fallback随机数生成器
            int newSeed = GenerateEntropySeed();
            fallbackRandom.GetType().GetField("seed",
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance)
                ?.SetValue(fallbackRandom, newSeed);
        }

        public void Dispose()
        {
            rng?.Dispose();
        }
    }

    public class SelectedRangeLinesIndicator : Indicator, IVolumeAnalysisIndicator
    {
        

        [InputParameter("Quantity", 1, 0.01, 9999999, 0.01, 2)]
        public double Quantity = 1.00;

        // 新增參數：微觀橫盤週期
        [InputParameter("Fibo Type", 2)]
        public int FiboType = 1;

        [InputParameter("EvalProfitTarget", 1, 0.01, 99999, 0.01, 2)]
        public double EvalProfitTarget = 4500.00;
        public int CardsCount = 3;



        private Point startPoint = Point.Empty;
        private Point endPoint = Point.Empty;
        private bool isDrawing = false;

        // 添加時間範圍變量，用於讓矩形跟著K線移動
        private DateTime savedStartTime = DateTime.MinValue;
        private DateTime savedEndTime = DateTime.MinValue;

        // 添加選擇輪相關變量
        private bool showFiboWheel = false;
        private Point wheelCenter = Point.Empty;
        private int wheelRadius = 80;
        private int hoveredWheelOption = -1; // -1表示沒有懸停，0-3表示選項
        private readonly string[] wheelOptions = { "FiboType 0", "FiboType 1", "FiboType 2", "Exit" };
        private double goldenRatio = ((1 + Math.Sqrt(5)) / 2);
        private double goldenFraction = 1 / ((1 + Math.Sqrt(5)) / 2); // 0.618033988749895
        private double goldenFractionComplement = 1 - 1 / ((1 + Math.Sqrt(5)) / 2); // 0.381966011250105
        private double fibo2618 = 1 + ((1 + Math.Sqrt(5)) / 2);



        private bool IsMode = true;

        private LineLevel LowestLine;
        private LineLevel HighestLine;
        private LineLevel LowerFiboInside;
        private LineLevel HigherFiboInside;
        private LineLevel HigherFibo1;
        private LineLevel LowerFibo1;
        private LineLevel HigherFibo2;
        private LineLevel LowerFibo2;
        private LineLevel HigherFibo3;
        private LineLevel LowerFibo3;
        private LineLevel HigherFibo1StopLossLine;
        private LineLevel LowerFibo1StopLossLine;
        private LineLevel HigherFibo2StopLossLine;
        private LineLevel LowerFibo2StopLossLine;

        private LineLevel HigherFibo3StopLossLine;
        private LineLevel LowerFibo3StopLossLine;

        private LineLevel HigherFibo111Line;
        private LineLevel LowerFibo111Line;
        private LineLevel HigherFibo211Line;
        private LineLevel LowerFibo211Line;

        private LineLevel HigherFibo311Line;
        private LineLevel LowerFibo311Line;

        private LineLevel EvalProfitTargetLine;
        private string Question = "";

        private Indicator atr;
        // Tarot card functionality - 使用加密安全的随机数生成器
        private CryptoSecureRandom random = new CryptoSecureRandom();
        private List<TarotCard> tarotCards = new List<TarotCard>
        {
            new TarotCard("The Fool", "愚者"),
            new TarotCard("The Magician", "魔術師"),
            new TarotCard("The High Priestess", "女祭司"),
            new TarotCard("The Empress", "皇后"),
            new TarotCard("The Emperor", "國王"),
            new TarotCard("The Hierophant", "教皇"),
            new TarotCard("The Lovers", "戀人"),
            new TarotCard("The Chariot", "戰車"),
            new TarotCard("Strength", "力量"),
            new TarotCard("The Hermit", "隱士"),
            new TarotCard("Wheel of Fortune", "命運之輪"),
            new TarotCard("Justice", "正義"),
            new TarotCard("The Hanged Man", "倒吊人"),
            new TarotCard("Death", "死神"),
            new TarotCard("Temperance", "節制"),
            new TarotCard("The Devil", "惡魔"),
            new TarotCard("The Tower", "高塔"),
            new TarotCard("The Star", "星星"),
            new TarotCard("The Moon", "月亮"),
            new TarotCard("The Sun", "太陽"),
            new TarotCard("Judgement", "審判"),
            new TarotCard("The World", "世界"),

             //Wands
            new TarotCard("Ace of Wands", "權杖一"),
            new TarotCard("Two of Wands", "權杖二"),
            new TarotCard("Three of Wands", "權杖三"),
            new TarotCard("Four of Wands", "權杖四"),
            new TarotCard("Five of Wands", "權杖五"),
            new TarotCard("Six of Wands", "權杖六"),
            new TarotCard("Seven of Wands", "權杖七"),
            new TarotCard("Eight of Wands", "權杖八"),
            new TarotCard("Nine of Wands", "權杖九"),
            new TarotCard("Ten of Wands", "權杖十"),
            new TarotCard("Page of Wands", "權杖侍者"),
            new TarotCard("Knight of Wands", "權杖騎士"),
            new TarotCard("Queen of Wands", "權杖皇后"),
            new TarotCard("King of Wands", "權杖國王"),

            //Cups
            new TarotCard("Ace of Cups", "聖杯一"),
            new TarotCard("Two of Cups", "聖杯二"),
            new TarotCard("Three of Cups", "聖杯三"),
            new TarotCard("Four of Cups", "聖杯四"),
            new TarotCard("Five of Cups", "聖杯五"),
            new TarotCard("Six of Cups", "聖杯六"),
            new TarotCard("Seven of Cups", "聖杯七"),
            new TarotCard("Eight of Cups", "聖杯八"),
            new TarotCard("Nine of Cups", "聖杯九"),
            new TarotCard("Ten of Cups", "聖杯十"),
            new TarotCard("Page of Cups", "聖杯侍者"),
            new TarotCard("Knight of Cups", "聖杯騎士"),
            new TarotCard("Queen of Cups", "聖杯皇后"),
            new TarotCard("King of Cups", "聖杯國王"),

            //Swords
            new TarotCard("Ace of Swords", "寶劍一"),
            new TarotCard("Two of Swords", "寶劍二"),
            new TarotCard("Three of Swords", "寶劍三"),
            new TarotCard("Four of Swords", "寶劍四"),
            new TarotCard("Five of Swords", "寶劍五"),
            new TarotCard("Six of Swords", "寶劍六"),
            new TarotCard("Seven of Swords", "寶劍七"),
            new TarotCard("Eight of Swords", "寶劍八"),
            new TarotCard("Nine of Swords", "寶劍九"),
            new TarotCard("Ten of Swords", "寶劍十"),
            new TarotCard("Page of Swords", "寶劍侍者"),
            new TarotCard("Knight of Swords", "寶劍騎士"),
            new TarotCard("Queen of Swords", "寶劍皇后"),
            new TarotCard("King of Swords", "寶劍國王"),

            //Pentacles
             new TarotCard("Ace of Pentacles", "錢幣一"),
            new TarotCard("Two of Pentacles", "錢幣二"),
            new TarotCard("Three of Pentacles", "錢幣三"),
            new TarotCard("Four of Pentacles", "錢幣四"),
            new TarotCard("Five of Pentacles", "錢幣五"),
            new TarotCard("Six of Pentacles", "錢幣六"),
            new TarotCard("Seven of Pentacles", "錢幣七"),
            new TarotCard("Eight of Pentacles", "錢幣八"),
            new TarotCard("Nine of Pentacles", "錢幣九"),
            new TarotCard("Ten of Pentacles", "錢幣十"),
            new TarotCard("Page of Pentacles", "錢幣侍者"),
            new TarotCard("Knight of Pentacles", "錢幣騎士"),
            new TarotCard("Queen of Pentacles", "錢幣皇后"),
            new TarotCard("King of Pentacles", "錢幣國王")



        };
        private List<TarotCard> drawnCards = new List<TarotCard>();
        private bool showTarot = false; // Flag to control whether to show the tarot reading

        private bool isShuffling = false; // Flag to indicate if shuffling animation is in progress
        private int shuffleCounter = 0; // Counter for shuffling animation

        // 新增：圖片版本的塔羅牌顯示
        private bool useImageDisplay = true; // 控制是否使用圖片顯示
        private Dictionary<string, System.Drawing.Image> tarotImages = new Dictionary<string, System.Drawing.Image>();
        private System.Drawing.Image cardBackImage = null;
        private string apiResponse = null;
        private const string ApiEndpoint = "https://tarot.ggwp.trade/api/tarot"; // Replace with your actual API endpoint
        private readonly HttpClient httpClient = new HttpClient();
        private CancellationTokenSource cancellationTokenSource = new CancellationTokenSource();

        // 新增：78張牌扇形排列選擇相關變數
        private List<TarotCard> allCards = new List<TarotCard>();
        private bool showCardSelection = false;
        private List<int> selectedCardIndices = new List<int>();
        private int hoveredCardIndex = -1;
        private const int MaxSelectedCards = 3;
        private DateTime shuffleStartTime;

        // 拖拽相關變數
        private bool isDraggingCard = false;
        private int draggedCardIndex = -1;
        private Point dragStartPosition = Point.Empty;
        private Point currentDragPosition = Point.Empty;
        private RectangleF dropZone = RectangleF.Empty; // 中央圓形抽牌區域

        // 添加共用的繪圖參數
        private readonly float cardWidth = 100;
        private readonly float cardHeight = 30;
        private readonly float cardChineseHeight = 20;
        private readonly float cardSpacing = 110;

        // 在類的開頭添加新的字段來存儲當前輪次的加密文字
        private class CardEncryption
        {
            public string EncryptedText { get; set; }
            public string EncryptedChineseText { get; set; }
        }
        private Dictionary<int, CardEncryption> currentRoundEncryption = new Dictionary<int, CardEncryption>();
        private readonly string[] mysticalSymbols = { "★", "☆", "✧", "✦", "✮", "✯", "❂", "✤", "✣", "❉", "❋", "❆", "☯", "☮", "卍", "〷", "☸", "⚝" };
        private readonly string[] chineseMysticalSymbols = {
            "乾", "坤", "震", "巽", "坎", "離", "艮", "兌",  // 八卦
            "陰", "陽", "太", "極",                          // 陰陽
            "木", "火", "土", "金", "水",                    // 五行
            "子", "丑", "寅", "卯", "辰", "巳",
            "午", "未", "申", "酉", "戌", "亥"              // 地支
        };

        private Color transparentGray = Color.FromArgb(25, Color.Gray);

        // 在類的開頭添加新的字段來存儲卡片位移
        private Dictionary<int, PointF> cardOffsets = new Dictionary<int, PointF>();
        private Random offsetRandom = new Random();
        private Random shuffleRandom = new Random();

        // 將這些變量移到類的成員變量位置
        private double savedHighestPrice = double.MinValue;
        private double savedLowestPrice = double.MaxValue;

        // 保存參與計算的K線範圍的最高最低價格（用於矩形顯示）
        private double savedRangeHighestPrice = double.MinValue;
        private double savedRangeLowestPrice = double.MaxValue;

        // 添加VAH和VAL相關變量
        private double vah = 0; // Value Area High
        private double val = 0; // Value Area Low
        private double poc = 0; // Point of Control
        private TimeZoneInfo _EasternZone = TimeZoneInfo.FindSystemTimeZoneById("Eastern Standard Time");

        // 添加Volume Analysis相關變量
        private IVolumeAnalysisCalculationProgress volumeAnalysisProgress;

        // IVolumeAnalysisIndicator 接口實現
        public bool IsRequirePriceLevelsCalculation => true;

        // 在類的開頭添加新的常量
        private const string LOCAL_AI_ENDPOINT = "http://127.0.0.1:4891/v1/chat/completions";
        private const string LOCAL_AI_KEY = "1234";
        private const string ORDER_API_ENDPOINT = "http://127.0.0.1:5000/update_order_form";
        private const string APIX_ENDPOINT = "http://127.0.0.1:8168/place_order";

        // 在類中添加按鈕相關的字段
        
        private bool isButtonHovered = false;

        // 添加總開關按鈕相關的字段
        private Rectangle masterButtonRect;
        private bool isMasterButtonEnabled = false;
        private bool isMasterButtonHovered = false;

        


        // 添加1比1模式按鈕相關的字段
        private Rectangle Mode11ButtonRect;
        private bool is11Mode = false;
        private bool isMode11ButtonHovered = false;



        // 添加長短切換按鈕相關的字段
        private Rectangle tradeModeButtonRect;
        private bool isLongMode = false;
        private bool isTradeModeButtonHovered = false;

        // 修改按鈕相關的字段定義
        private Rectangle sellOrderButton2618;  // 2.618線的賣出按鈕
        private Rectangle sellOrderButton1618;  // 1.618線的賣出按鈕
        private Rectangle sellOrderButton1382;  // 1.382線的賣出按鈕
        private Rectangle buyOrderButton2618;   // 2.618線的買入按鈕
        private Rectangle buyOrderButton1618;   // 1.618線的買入按鈕
        private Rectangle buyOrderButton1382;   // 1.382線的買入按鈕
        private bool isSellButton2618Hovered = false;
        private bool isSellButton1618Hovered = false;
        private bool isSellButton1382Hovered = false;
        private bool isBuyButton2618Hovered = false;
        private bool isBuyButton1618Hovered = false;
        private bool isBuyButton1382Hovered = false;

        // 添加一個枚舉來標識按鈕類型
        private enum HoveredButton
        {
            None,
            Refresh,
            SellButton2618,
            SellButton1618,
            SellButton1382,
            BuyButton2618,
            BuyButton1618,
            BuyButton1382
        }

        // 添加一個變量來追踪最後懸停的按鈕
        private HoveredButton lastHoveredButton = HoveredButton.None;

        // 添加按鈕狀態枚舉
        private enum ButtonState
        {
            Normal,
            Success,
            Failed
        }

        // 添加按鈕狀態追踪
        private Dictionary<HoveredButton, ButtonState> buttonStates = new Dictionary<HoveredButton, ButtonState>();
        private Dictionary<HoveredButton, DateTime> buttonStateTimers = new Dictionary<HoveredButton, DateTime>();
        private const int STATE_DISPLAY_DURATION_MS = 2000; // 狀態顯示持續2秒

        private Point currentMousePosition;

        private bool showButtons = false;
        private Point buttonPosition = Point.Empty;
        private HoveredButton activeButton = HoveredButton.None;
        private const int PRICE_HOVER_THRESHOLD = 25; // 增加價格線懸停檢測範圍（像素）
        private const int BUTTON_OFFSET_X = 10; // 按鈕與鼠標的水平偏移
        private const int BUTTON_OFFSET_Y = 5;  // 按鈕與鼠標的垂直偏移

        // 添加鼠标按下检测相关字段
        private DateTime mouseDownTime = DateTime.MinValue;
        private Point lastMousePosition = Point.Empty;
        private DateTime mouseLeaveTime = DateTime.MinValue; // 鼠標離開圖表的時間
        private const int MOUSE_LEAVE_TIMEOUT_MS = 1000; // 鼠标離開圖表1000毫秒后自动完成选择
        private const int MIN_MOUSE_MOVE_DISTANCE = 5; // 最小鼠标移动距离（像素）

        // 修改按鈕顏色
        private readonly Color sellButton1382Color = Color.FromArgb(180, 255, 150, 150);    // 淺紅色
        private readonly Color sellButton1618Color = Color.FromArgb(180, 255, 50, 50);      // 深紅色
        private readonly Color sellButton2618Color = Color.FromArgb(180, 255, 140, 0);      // 橙色
        private readonly Color buyButton1382Color = Color.FromArgb(180, 150, 255, 150);     // 淺綠色
        private readonly Color buyButton1618Color = Color.FromArgb(180, 50, 255, 50);       // 深綠色
        private readonly Color buyButton2618Color = Color.FromArgb(180, 255, 140, 0);       // 橙色

        // 調整hover顏色，使其變化更明顯
        private readonly Color sellButtonHoverColor = Color.FromArgb(230, 255, 100, 100);   // 加深的紅色
        private readonly Color buyButtonHoverColor = Color.FromArgb(230, 100, 255, 100);    // 加深的綠色
        private readonly Color orangeButtonHoverColor = Color.FromArgb(230, 255, 160, 20);  // 加深的橙色

        public SelectedRangeLinesIndicator()
            : base()
        {
            Name = "Selected_Range_Lines_Indicator";
            Description = "Draw lines at highest and lowest close in selected range";
            this.LowestLine = this.AddLineLevel(0.0, "SelectedRangePriceLow", Color.OldLace, 1, LineStyle.Dash);
            this.HighestLine = this.AddLineLevel(0.0, "SelectedRangePriceHigh", Color.OldLace, 1, LineStyle.Dash);
            this.HigherFiboInside = this.AddLineLevel(0.0, "SelectedRangePriceFiboLow0", Color.Yellow);
            this.LowerFiboInside = this.AddLineLevel(0.0, "SelectedRangePriceFiboHigh0", Color.Yellow);
            this.LowerFibo1 = this.AddLineLevel(0.0, "SelectedRangePriceFiboLow", Color.Yellow);
            this.HigherFibo1 = this.AddLineLevel(0.0, "SelectedRangePriceFiboHigh", Color.Yellow);
            this.LowerFibo2 = this.AddLineLevel(0.0, "SelectedRangePriceFiboLow2", Color.Yellow);
            this.HigherFibo2 = this.AddLineLevel(0.0, "SelectedRangePriceFiboHigh2", Color.Yellow);
            this.LowerFibo3 = this.AddLineLevel(0.0, "SelectedRangePriceFiboLow3", Color.Orange);
            this.HigherFibo3 = this.AddLineLevel(0.0, "SelectedRangePriceFiboHigh3", Color.Orange);
            this.HigherFibo1StopLossLine = this.AddLineLevel(0.0, "SelectedRangePriceStopLow0", Color.Red, 1, LineStyle.Dash);
            this.LowerFibo1StopLossLine = this.AddLineLevel(0.0, "SelectedRangePriceStopHigh0", Color.Red, 1, LineStyle.Dash);
            this.HigherFibo2StopLossLine = this.AddLineLevel(0.0, "SelectedRangePriceStopLow", Color.Red, 1, LineStyle.Dash);
            this.LowerFibo2StopLossLine = this.AddLineLevel(0.0, "SelectedRangePriceStopHigh", Color.Red, 1, LineStyle.Dash);

            this.HigherFibo3StopLossLine = this.AddLineLevel(0.0, "SelectedRangePriceStopLow2", Color.Red, 1, LineStyle.Dash);
            this.LowerFibo3StopLossLine = this.AddLineLevel(0.0, "SelectedRangePriceStopHigh2", Color.Red, 1, LineStyle.Dash);


            this.HigherFibo111Line = this.AddLineLevel(0.0, "SelectedRangePriceStopLow0", Color.Yellow, 1, LineStyle.Dash);
            this.LowerFibo111Line = this.AddLineLevel(0.0, "SelectedRangePriceStopHigh0", Color.Yellow, 1, LineStyle.Dash);
            this.HigherFibo211Line = this.AddLineLevel(0.0, "SelectedRangePriceStopLow", Color.Yellow, 1, LineStyle.Dash);
            this.LowerFibo211Line = this.AddLineLevel(0.0, "SelectedRangePriceStopHigh", Color.Yellow, 1, LineStyle.Dash);

            this.HigherFibo311Line = this.AddLineLevel(0.0, "SelectedRangePriceStopLow2", Color.Yellow, 1, LineStyle.Dash);
            this.LowerFibo311Line = this.AddLineLevel(0.0, "SelectedRangePriceStopHigh2", Color.Yellow, 1, LineStyle.Dash);

            this.EvalProfitTargetLine = this.AddLineLevel(0.0, "SelectedRangePriceEvalProfitTarget", Color.Blue, 1, LineStyle.Dash);

            SeparateWindow = false;

            // 初始化總開關按鈕位置
            masterButtonRect = new Rectangle(0, 0, 100, 30); // 位置會在OnPaintChart中更新

            

            // 初始化考試模式按鈕位置（長短切換按鈕左側）
            Mode11ButtonRect = new Rectangle(0, 0, 100, 30); // 位置會在OnPaintChart中更新

            // 初始化長短切換按鈕位置（總開關按鈕左側）
            tradeModeButtonRect = new Rectangle(0, 0, 100, 30); // 位置會在OnPaintChart中更新

            // 初始化更小的按鈕，X位置設在右邊
            int rightEdgeX = 10;  // 這個值會在OnPaintChart中被更新
            sellOrderButton2618 = new Rectangle(rightEdgeX, 100, 60, 25);
            sellOrderButton1618 = new Rectangle(rightEdgeX, 100, 60, 25);
            sellOrderButton1382 = new Rectangle(rightEdgeX, 100, 60, 25);
            buyOrderButton2618 = new Rectangle(rightEdgeX, 140, 60, 25);
            buyOrderButton1618 = new Rectangle(rightEdgeX, 140, 60, 25);
            buyOrderButton1382 = new Rectangle(rightEdgeX, 140, 60, 25);
        }
        // 新增重置狀態的方法
        private void ResetAllStates()
        {
            // 取消當前的解牌請求
            cancellationTokenSource.Cancel();
            cancellationTokenSource = new CancellationTokenSource();

            // 重置到初始狀態
            IsMode = true;
            showTarot = false;
            isShuffling = false;
            showCardSelection = false;
            apiResponse = null;
            drawnCards.Clear();
            allCards.Clear();
            selectedCardIndices.Clear();
            hoveredCardIndex = -1;
            currentRoundEncryption.Clear();
            cardOffsets.Clear();

            // 重置範圍選擇相關的變量
            startPoint = Point.Empty;
            endPoint = Point.Empty;
            isDrawing = false;
            mouseDownTime = DateTime.MinValue; // 重置计时器
            mouseLeaveTime = DateTime.MinValue; // 重置離開計時器
            savedStartTime = DateTime.MinValue;
            savedEndTime = DateTime.MinValue;

            // 重置選擇輪
            showFiboWheel = false;
            hoveredWheelOption = -1;
            
            // 重置所有線條的值為0（使其不顯示）
            this.HighestLine.Level = 0;
            this.LowestLine.Level = 0;
            this.LowerFiboInside.Level = 0;
            this.HigherFiboInside.Level = 0;
            this.HigherFibo1.Level = 0;
            this.LowerFibo1.Level = 0;
            this.HigherFibo2.Level = 0;
            this.LowerFibo2.Level = 0;
            this.HigherFibo3.Level = 0;
            this.LowerFibo3.Level = 0;
            this.HigherFibo1StopLossLine.Level = 0;
            this.LowerFibo1StopLossLine.Level = 0;
            this.HigherFibo2StopLossLine.Level = 0;
            this.LowerFibo2StopLossLine.Level = 0;
            this.HigherFibo3StopLossLine.Level = 0;
            this.LowerFibo3StopLossLine.Level = 0;

            this.HigherFibo111Line.Level = 0;
            this.LowerFibo111Line.Level = 0;
            this.HigherFibo211Line.Level = 0;
            this.LowerFibo211Line.Level = 0;
            this.HigherFibo311Line.Level = 0;
            this.LowerFibo311Line.Level = 0;

            this.EvalProfitTargetLine.Level = 0;

            // 重置保存的價格值
            savedHighestPrice = double.MinValue;
            savedLowestPrice = double.MaxValue;
            savedRangeHighestPrice = double.MinValue;
            savedRangeLowestPrice = double.MaxValue;

            // 重置VAH/VAL相關變量
            vah = 0;
            val = 0;
            poc = 0;

            this.CurrentChart.RedrawBuffer();
        }

        // IVolumeAnalysisIndicator 接口實現
        public void VolumeAnalysisData_Loaded()
        {
            // Volume Analysis 數據加載完成後的回調
            // 可以在這裡進行一些初始化工作
            this.CurrentChart.RedrawBuffer();
        }

        // 新增開始新洗牌流程的方法
        private void StartNewShuffling()
        {
            // 確保取消之前的所有操作
            cancellationTokenSource.Cancel();
            cancellationTokenSource = new CancellationTokenSource();

            // 開始洗牌流程
            isShuffling = true;
            shuffleCounter = 0;
            drawnCards.Clear();
            showTarot = false;
            apiResponse = null;
            cardOffsets.Clear();
            GenerateNewEncryption();

            _ = ContinuousShuffling(cancellationTokenSource.Token);
        }
        protected override void OnInit()
        {
            if(Symbol.SymbolType==SymbolType.Futures)
            {

            }
            else 
            {

                if(this.Quantity < Symbol.MinLot)
                {
                        this.Quantity = Symbol.MinLot;

                }
            }
            this.Quantity = Convert.ToDouble(Symbol.FormatQuantity(this.Quantity, !(Symbol.SymbolType==SymbolType.Futures)));
            // 初始化基本變量
            startPoint = Point.Empty;
            endPoint = Point.Empty;
            isDrawing = false;
            goldenRatio = (1 + Math.Sqrt(5)) / 2;
            goldenFraction = 1 / goldenRatio;
            goldenFractionComplement = 1 - goldenFraction;
            fibo2618 = 1 + goldenRatio;

            

            // 確保狀態正確初始化
            IsMode = true;
            showTarot = false;
            isShuffling = false;

            // 初始化或重新初始化集合
            if (cardOffsets == null) cardOffsets = new Dictionary<int, PointF>();
            if (offsetRandom == null) offsetRandom = new Random();
            if (shuffleRandom == null) shuffleRandom = new Random();
            if (random == null) random = new CryptoSecureRandom();
            if (drawnCards == null) drawnCards = new List<TarotCard>();
            if (allCards == null) allCards = new List<TarotCard>();
            if (selectedCardIndices == null) selectedCardIndices = new List<int>();

            // 清空現有數據
            cardOffsets.Clear();
            drawnCards.Clear();

            // 重置其他狀態
            shuffleCounter = 0;
            apiResponse = null;

            // 確保取消令牌源被正確初始化
            if (cancellationTokenSource != null)
            {
                cancellationTokenSource.Cancel();
                cancellationTokenSource.Dispose();
            }
            cancellationTokenSource = new CancellationTokenSource();

            // 註冊事件處理器前先取消註冊，避免重複
            this.CurrentChart.MouseDown -= CurrentChart_MouseDown;
            this.CurrentChart.MouseUp -= CurrentChart_MouseUp;
            this.CurrentChart.MouseMove -= CurrentChart_MouseMove;
            this.CurrentChart.MouseLeave -= CurrentChart_MouseLeave;

            // 重新註冊事件處理器
            this.CurrentChart.MouseDown += CurrentChart_MouseDown;
            this.CurrentChart.MouseUp += CurrentChart_MouseUp;
            this.CurrentChart.MouseMove += CurrentChart_MouseMove;
            this.CurrentChart.MouseLeave += CurrentChart_MouseLeave;

            // 初始化ATR指標
            //atr = Core.Indicators.BuiltIn.ATR(2000, MaMode.SMA);
            //this.AddIndicator(atr);

            Question = "對於此標的:" + Symbol.Name + "這次的交易機會如果執行我的策略介入，我的情緒會變得怎樣？（三張牌代表進場、持倉過程、出場時的情緒，最重要的是出場的情緒，如果失落就意味著不值得介入）本次機會是否適合進行介入？是否值得介入？";

            // 加載塔羅牌圖片
            LoadTarotImages();
        }

        // 在開始洗牌時生成新的加密文字
        private void GenerateNewEncryption()
        {
            foreach (var card in tarotCards)
            {
                // 為英文名加密
                string encryptedEnglish = "☯"; // 始於太極
                for (int i = 0; i < card.EnglishName.Length; i++)
                {
                    // 混合使用神秘符號和易經元素
                    if (i % 2 == 0)
                    {
                        encryptedEnglish += mysticalSymbols[random.Next(mysticalSymbols.Length)];
                    }
                    else
                    {
                        encryptedEnglish += chineseMysticalSymbols[random.Next(8)]; // 使用八卦
                    }
                }
                card.EncryptedEnglishName = encryptedEnglish;

                // 為中文名加密
                string encryptedChinese = "";
                // 加入五行元素
                encryptedChinese += chineseMysticalSymbols[random.Next(12, 17)]; // 五行
                // 加入天干地支
                encryptedChinese += chineseMysticalSymbols[random.Next(17, chineseMysticalSymbols.Length)];

                // 根據原始名稱長度添加其他易經元素
                for (int i = 0; i < card.ChineseName.Length - 2; i++)
                {
                    encryptedChinese += chineseMysticalSymbols[random.Next(chineseMysticalSymbols.Length)];
                }

                card.EncryptedChineseName = encryptedChinese;
            }
        }
        private void CurrentChart_MouseDown(object sender, TradingPlatform.BusinessLayer.Chart.ChartMouseNativeEventArgs e)
        {
            if (e.Button == TradingPlatform.BusinessLayer.Native.NativeMouseButtons.Left)
            {

            }
            else if (e.Button == TradingPlatform.BusinessLayer.Native.NativeMouseButtons.Right)
            {

                // 處理78張牌選擇界面的拖拽開始
                                if (showCardSelection)
                                {
                                    int clickedCardIndex = GetHoveredCardIndex(e.Location);
                                    if (clickedCardIndex >= 0 && clickedCardIndex < allCards.Count)
                                    {
                                        // 開始拖拽
                                        isDraggingCard = true;
                                        draggedCardIndex = clickedCardIndex;
                                        dragStartPosition = e.Location;
                                        currentDragPosition = e.Location;
                                        this.CurrentChart.RedrawBuffer();
                                        return;
                                    }
                                }
                // 如果不在按鈕上，則檢查是否在視窗外
                if (!this.CurrentChart.MainWindow.ClientRectangle.Contains(e.Location))
                {
                    if (showTarot || !IsMode)
                    {
                        ResetAllStates();
                    }
                    else if (IsMode)
                    {
                        StartNewShuffling();
                    }
                }
                else if (IsMode)  // 如果在視窗內且處於選擇模式
                {
                    DateTime clickTime = this.CurrentChart.MainWindow.CoordinatesConverter.GetTime(e.Location.X);
                    DateTime lastBarTime = this.HistoricalData[0].TimeLeft;

                    if (clickTime > lastBarTime)
                    {
                        // 在未來區域顯示FiboType選擇輪
                        showFiboWheel = true;
                        wheelCenter = e.Location;
                        hoveredWheelOption = -1;
                        this.CurrentChart.RedrawBuffer();
                        return;
                    }

                    startPoint = e.Location;
                    isDrawing = true;

                    // 记录鼠标按下的时间和位置
                    mouseDownTime = DateTime.Now;
                    lastMousePosition = e.Location;

                    // 重置ATR指標
                    //if (atr != null)
                    //{
                        //this.RemoveIndicator(atr);
                    //}
                }
            }
        }

        private void CurrentChart_MouseUp(object sender, TradingPlatform.BusinessLayer.Chart.ChartMouseNativeEventArgs e)
        {
            if (e.Button == TradingPlatform.BusinessLayer.Native.NativeMouseButtons.Left)
            {


                // 檢查是否點擊了長短切換按鈕
                if (tradeModeButtonRect.Contains(e.Location))
                {
                    isLongMode = !isLongMode;
                    this.CurrentChart.RedrawBuffer();
                    return;
                }

                // 檢查是否點擊了長短切換按鈕
                if (Mode11ButtonRect.Contains(e.Location))
                {
                    is11Mode = !is11Mode;
                    this.CurrentChart.RedrawBuffer();
                    return;
                }

                // 檢查是否點擊了總開關按鈕
                if (masterButtonRect.Contains(e.Location))
                {
                    isMasterButtonEnabled = !isMasterButtonEnabled;
                    this.CurrentChart.RedrawBuffer();
                    return;
                }

                // 只有在總開關啟用時才處理其他按鈕點擊
                if (isMasterButtonEnabled)
                {
                    if (isShuffling)
                    {
                        StopShufflingAndStartReading();
                        return;
                    }

                    // 檢查是否點擊了按鈕
                    if (showButtons)
                    {
                        Rectangle leftButtonRect = new Rectangle(buttonPosition.X - 165, buttonPosition.Y - 12, 80, 25);
                        Rectangle middleButtonRect = new Rectangle(buttonPosition.X - 80, buttonPosition.Y - 12, 80, 25);
                        Rectangle rightButtonRect = new Rectangle(buttonPosition.X + 5, buttonPosition.Y - 12, 80, 25);

                        if (leftButtonRect.Contains(e.Location))
                        {
                            HandleOrderButton();
                        }
                        else if (middleButtonRect.Contains(e.Location))
                        {
                            HandleApiButton();
                        }
                        else if (rightButtonRect.Contains(e.Location))
                        {
                            HandleApixButton();
                        }
                        else
                        {
                            CheckAndShowButtons(e.Location);
                        }
                    }
                    else
                    {
                        CheckAndShowButtons(e.Location);
                    }
                }
            }
            if (e.Button == TradingPlatform.BusinessLayer.Native.NativeMouseButtons.Right)
            {
                // 處理78張牌選擇界面的拖拽結束
                                if (showCardSelection && isDraggingCard)
                                {
                                    // 檢查是否拖拽到抽牌區域
                                    if (IsPointInDropZone(e.Location))
                                    {
                                        // 成功抽牌
                                        HandleCardSelection(draggedCardIndex);
                                    }

                                    // 重置拖拽狀態
                                    isDraggingCard = false;
                                    draggedCardIndex = -1;
                                    dragStartPosition = Point.Empty;
                                    currentDragPosition = Point.Empty;
                                    this.CurrentChart.RedrawBuffer();
                                    return;
                                }

                                // 處理78張牌選擇界面的點擊（非拖拽情況下保留原有功能）
                                if (showCardSelection && !isDraggingCard)
                                {
                                    int clickedCardIndex = GetHoveredCardIndex(e.Location);
                                    if (clickedCardIndex >= 0 && clickedCardIndex < allCards.Count)
                                    {
                                        HandleCardSelection(clickedCardIndex);
                                        return;
                                    }
                                }
                // 處理選擇輪的選擇
                if (showFiboWheel)
                {
                    // 計算點到輪心的距離
                    double dx = e.Location.X - wheelCenter.X;
                    double dy = e.Location.Y - wheelCenter.Y;
                    double distance = Math.Sqrt(dx * dx + dy * dy);

                    if (distance < 20)
                    {
                        // 在內圓範圍內提起 = Exit
                        HandleWheelSelection(3); // Exit
                    }
                    else
                    {
                        // 在外圓範圍內，根據角度選擇
                        int selectedOption = GetWheelOptionAtPoint(e.Location);
                        if (selectedOption >= 0)
                        {
                            HandleWheelSelection(selectedOption);
                        }
                        else
                        {
                            // 如果沒有選中任何選項，默認Exit
                            HandleWheelSelection(3);
                        }
                    }
                    showFiboWheel = false;
                    this.CurrentChart.RedrawBuffer();
                    return;
                }

                // 無論在哪裡釋放右鍵，都執行相應的操作
                if (isShuffling)
                {
                    // 洗牌時，無論在Chart內外釋放都停止洗牌
                    StopShufflingAndStartReading();
                }
                else if (isDrawing)
                {
                    // 選擇範圍時，無論在Chart內外釋放都完成選擇
                    // MouseUp時直接使用當前的endPoint，不需要重新設置
                    // 因為endPoint已經在MouseMove中正確處理了範圍限制
                    isDrawing = false;
                    mouseDownTime = DateTime.MinValue; // 重置计时器
                    mouseLeaveTime = DateTime.MinValue; // 重置離開計時器
                    this.CurrentChart.RedrawBuffer();
                }
            }
        }

        // 新增一個方法來處理停止洗牌和開始解牌的邏輯
        private void StopShufflingAndStartReading()
        {
            isShuffling = false;
            showTarot = false; // 先不顯示解牌結果
            showCardSelection = true; // 顯示78張牌選擇界面
            IsMode = false;

            // 初始化78張牌的排列
            InitializeAllCards();
            selectedCardIndices.Clear();
            hoveredCardIndex = -1;

            this.CurrentChart.RedrawBuffer();
        }

        // 初始化78張牌的隨機排列
        private void InitializeAllCards()
        {
            allCards.Clear();
            allCards.AddRange(tarotCards);

            // 使用Fisher-Yates洗牌算法隨機排列
            for (int i = allCards.Count - 1; i > 0; i--)
            {
                int j = shuffleRandom.Next(i + 1);
                var temp = allCards[i];
                allCards[i] = allCards[j];
                allCards[j] = temp;
            }
        }

        // 處理卡片選擇
        private void HandleCardSelection(int cardIndex)
        {
            if (selectedCardIndices.Contains(cardIndex))
            {
                // 如果已經選中，則取消選擇
                selectedCardIndices.Remove(cardIndex);
            }
            else if (selectedCardIndices.Count < MaxSelectedCards)
            {
                // 如果未選中且未達到最大選擇數量，則選中
                selectedCardIndices.Add(cardIndex);
            }

            // 如果選擇了3張牌，開始解牌
            if (selectedCardIndices.Count == MaxSelectedCards)
            {
                StartTarotReading();
            }

            this.CurrentChart.RedrawBuffer();
        }

        // 檢查點是否在抽牌區域內
        private bool IsPointInDropZone(Point point)
        {
            if (dropZone.IsEmpty) return false;

            // 計算點到圓心的距離
            float centerX = dropZone.X + dropZone.Width / 2;
            float centerY = dropZone.Y + dropZone.Height / 2;
            float radius = dropZone.Width / 2;

            float distance = (float)Math.Sqrt(Math.Pow(point.X - centerX, 2) + Math.Pow(point.Y - centerY, 2));
            return distance <= radius;
        }

        // 初始化抽牌區域
        private void InitializeDropZone(Rectangle clientRect)
        {
            // 在屏幕中央創建一個圓形抽牌區域
            float radius = Math.Min(clientRect.Width, clientRect.Height) * 0.15f; // 屏幕尺寸的15%
            float centerX = clientRect.X + clientRect.Width / 2 - radius;
            float centerY = clientRect.Y + clientRect.Height / 2 - radius;

            dropZone = new RectangleF(centerX, centerY, radius * 2, radius * 2);
        }

        // 繪製抽牌區域
        private void DrawDropZone(Graphics gr)
        {
            if (dropZone.IsEmpty) return;

            // 繪製圓形抽牌區域
            using (SolidBrush dropZoneBrush = new SolidBrush(Color.FromArgb(100, 255, 215, 0))) // 半透明金色
            using (Pen dropZonePen = new Pen(Color.Gold, 3))
            using (Font dropZoneFont = new Font("Arial", 14, FontStyle.Bold))
            using (SolidBrush textBrush = new SolidBrush(Color.White))
            using (StringFormat centerFormat = new StringFormat())
            {
                centerFormat.Alignment = StringAlignment.Center;
                centerFormat.LineAlignment = StringAlignment.Center;

                // 繪製圓形背景
                gr.FillEllipse(dropZoneBrush, dropZone);
                gr.DrawEllipse(dropZonePen, dropZone);

                // 繪製文字提示
                string text = $"抽牌區\n({selectedCardIndices.Count}/3)";
                gr.DrawString(text, dropZoneFont, textBrush, dropZone, centerFormat);
            }
        }

        // 繪製正在拖拽的卡片
        private void DrawDraggedCard(Graphics gr, float cardWidth, float cardHeight)
        {
            float cardX = currentDragPosition.X - cardWidth / 2f;
            float cardY = currentDragPosition.Y - cardHeight / 2f;
            RectangleF draggedCardRect = new RectangleF(cardX, cardY, cardWidth, cardHeight);

            using (SolidBrush draggedBrush = new SolidBrush(Color.FromArgb(220, 255, 255, 255))) // 半透明白色
            using (Pen draggedPen = new Pen(Color.Yellow, 2)) // 黃色邊框表示拖拽中
            using (Font cardFont = new Font("Arial", 7))
            using (SolidBrush textBrush = new SolidBrush(Color.Black))
            using (StringFormat centerFormat = new StringFormat())
            {
                centerFormat.Alignment = StringAlignment.Center;
                centerFormat.LineAlignment = StringAlignment.Center;

                // 繪製卡片背景
                gr.FillRectangle(draggedBrush, draggedCardRect);
                gr.DrawRectangle(draggedPen, Rectangle.Round(draggedCardRect));

                // 繪製卡片背面圖案或文字
                if (cardBackImage != null)
                {
                    // 先繪製底層的左右反轉牌面
                    if (tarotImages.Count > 0 && draggedCardIndex >= 0 && draggedCardIndex < allCards.Count)
                    {
                        // 使用對應卡片的圖像作為底層
                        var cardKey = allCards[draggedCardIndex].EnglishName;
                        if (tarotImages.ContainsKey(cardKey))
                        {
                            var randomCard = tarotImages[cardKey];

                        // 創建左右反轉的圖像
                        using (var flippedImage = new Bitmap((int)draggedCardRect.Width, (int)draggedCardRect.Height))
                        {
                            using (var tempGr = Graphics.FromImage(flippedImage))
                            {
                                // 設置左右反轉變換
                                tempGr.ScaleTransform(-1, 1);
                                tempGr.TranslateTransform(-draggedCardRect.Width, 0);

                                // 繪製反轉的牌面
                                tempGr.DrawImage(randomCard, 0, 0, draggedCardRect.Width, draggedCardRect.Height);
                            }

                            // 繪製到主畫布
                            gr.DrawImage(flippedImage, draggedCardRect);
                        }
                        }
                    }

                    // 在上層繪製97%透明度的牌背
                    var colorMatrix = new System.Drawing.Imaging.ColorMatrix();
                    colorMatrix.Matrix33 = 0.97f; // 設置透明度為97%
                    using (var imageAttributes = new System.Drawing.Imaging.ImageAttributes())
                    {
                        imageAttributes.SetColorMatrix(colorMatrix);
                        gr.DrawImage(cardBackImage, Rectangle.Round(draggedCardRect), 0, 0, cardBackImage.Width, cardBackImage.Height, GraphicsUnit.Pixel, imageAttributes);
                    }
                }
                else
                {
                    // 如果沒有背面圖片，顯示卡片編號
                    gr.DrawString((draggedCardIndex + 1).ToString(), cardFont, textBrush, draggedCardRect, centerFormat);
                }
            }
        }

        // 獲取鼠標懸停的卡片索引（基於水平位置平均分配）
        private int GetHoveredCardIndex(Point mouseLocation)
        {
            if (!showCardSelection || allCards.Count == 0)
                return -1;

            Rectangle mainWindow = this.CurrentChart.MainWindow.ClientRectangle;
            int totalCards = allCards.Count;
            float cardWidth = 100f;
            float cardHeight = 150f;

            // 計算扇形參數（與繪製時保持一致）
            float fanRadius = Math.Min(mainWindow.Width * 0.45f, 500f);
            float startAngle = 30f;
            float endAngle = 150f;
            float angleStep = (endAngle - startAngle) / (totalCards - 1);
            float centerX = mainWindow.Width / 2f;
            float centerY = mainWindow.Height * 0.28f;

            // 計算所有卡片的實際邊界
            float minX = float.MaxValue;
            float maxX = float.MinValue;
            float minY = float.MaxValue;
            float maxY = float.MinValue;

            for (int i = 0; i < totalCards; i++)
            {
                // 計算每張卡片的位置（與繪製時保持一致）
                float angle = startAngle + i * angleStep;
                float angleRad = angle * (float)Math.PI / 180f;

                float cardCenterX = centerX + fanRadius * (float)Math.Cos(angleRad);
                float cardCenterY = centerY + fanRadius * (float)Math.Sin(angleRad);

                // 計算卡片的邊界
                float cardLeft = cardCenterX - cardWidth / 2f;
                float cardRight = cardCenterX + cardWidth / 2f;
                float cardTop = cardCenterY - cardHeight / 2f;
                float cardBottom = cardCenterY + cardHeight / 2f;

                // 更新總邊界
                minX = Math.Min(minX, cardLeft);
                maxX = Math.Max(maxX, cardRight);
                minY = Math.Min(minY, cardTop);
                maxY = Math.Max(maxY, cardBottom);
            }

            // 設置寬限範圍（卡片區域外的額外檢測範圍）
            float toleranceWidth = (maxX - minX) * 0.3f; // 30%的寬限範圍
            float toleranceHeight = (maxY - minY) * 0.2f; // 20%的Y軸寬限範圍

            // 檢查鼠標是否在擴展的檢測範圍內
            if (mouseLocation.X < minX - toleranceWidth || mouseLocation.X > maxX + toleranceWidth ||
                mouseLocation.Y < minY - toleranceHeight || mouseLocation.Y > maxY + toleranceHeight)
            {
                // 超出寬限範圍，不選擇任何卡片
                return -1;
            }

            // 計算鼠標到每張卡片中心的距離，找到最接近的卡片
            float minDistance = float.MaxValue;
            int closestCardIndex = -1;

            for (int i = 0; i < totalCards; i++)
            {
                // 計算每張卡片的中心位置（與繪製時保持一致）
                float angle = startAngle + i * angleStep;
                float angleRad = angle * (float)Math.PI / 180f;

                float cardCenterX = centerX + fanRadius * (float)Math.Cos(angleRad);
                float cardCenterY = centerY + fanRadius * (float)Math.Sin(angleRad);

                // 計算鼠標到卡片中心的距離
                float deltaX = mouseLocation.X - cardCenterX;
                float deltaY = mouseLocation.Y - cardCenterY;
                float distance = (float)Math.Sqrt(deltaX * deltaX + deltaY * deltaY);

                // 更新最接近的卡片
                if (distance < minDistance)
                {
                    minDistance = distance;
                    closestCardIndex = i;
                }
            }

            return closestCardIndex;
        }

        // 開始塔羅牌解讀
        private void StartTarotReading()
        {
            showCardSelection = false;
            showTarot = true;

            // 將選中的卡片設為解讀卡片
            drawnCards.Clear();
            foreach (int index in selectedCardIndices)
            {
                drawnCards.Add(allCards[index]);
            }

            // 創建新的取消令牌來處理新的解牌請求
            var currentToken = cancellationTokenSource.Token;

            _ = FetchTarotReading(new List<TarotCard>(drawnCards), currentToken, Question)
                .ContinueWith(t =>
                {
                    if (t.IsCompleted && !t.IsFaulted && !currentToken.IsCancellationRequested)
                    {
                        apiResponse = t.Result;
                        this.CurrentChart.RedrawBuffer();
                    }
                }, TaskScheduler.Default);

            this.CurrentChart.RedrawBuffer();
        }

        // 繪製78張牌選擇界面
        private void DrawCardSelectionInterface(Graphics gr, Rectangle mainWindow)
        {
            if (allCards.Count == 0) return;

            // 初始化抽牌區域
            InitializeDropZone(mainWindow);

            // 繪製半透明背景
            using (SolidBrush backgroundBrush = new SolidBrush(Color.FromArgb(180, 0, 0, 0)))
            {
                gr.FillRectangle(backgroundBrush, mainWindow);
            }

            // 扇形排列參數
            int totalCards = allCards.Count;
            float cardWidth = 100f;  // 進一步增大卡片寬度，更清晰可見
            float cardHeight = 150f; // 進一步增大卡片高度，更清晰可見

            // 水平扇形參數
            float fanRadius = Math.Min(mainWindow.Width * 0.45f, 500f); // 進一步增大扇形半径，适应更大的卡片
            float fanAngle = 120f; // 扇形總角度（度）
            float startAngle = 30f; // 起始角度（從右上開始）
            float endAngle = 150f;  // 結束角度（到左上結束）
            float angleStep = (endAngle - startAngle) / (totalCards - 1); // 每張牌的角度間隔

            // 扇形中心點（調整到合適位置）
            float centerX = mainWindow.Width / 2f;
            float centerY = mainWindow.Height * 0.28f; // 向上移動扇形位置

            // 繪製標題
            using (Font titleFont = new Font("Arial", 16, FontStyle.Bold))
            using (SolidBrush titleBrush = new SolidBrush(Color.White))
            using (StringFormat centerFormat = new StringFormat())
            {
                centerFormat.Alignment = StringAlignment.Center;
                centerFormat.LineAlignment = StringAlignment.Center;

                string title = $"請選擇三張牌 ({selectedCardIndices.Count}/3)";
                RectangleF titleRect = new RectangleF(0, centerY - cardHeight/2f - 50, mainWindow.Width, 30);
                gr.DrawString(title, titleFont, titleBrush, titleRect, centerFormat);
            }

            // 繪製所有卡片
            using (Font cardFont = new Font("Arial", 7))
            using (SolidBrush cardBrush = new SolidBrush(Color.FromArgb(200, 70, 70, 70)))
            using (SolidBrush selectedBrush = new SolidBrush(Color.FromArgb(200, 255, 215, 0))) // 金色選中效果
            using (SolidBrush hoveredBrush = new SolidBrush(Color.FromArgb(150, 255, 255, 255))) // 白色懸停效果
            using (SolidBrush textBrush = new SolidBrush(Color.White))
            using (Pen borderPen = new Pen(Color.Gold, 1))
            using (Pen selectedPen = new Pen(Color.Red, 2))
            using (StringFormat centerFormat = new StringFormat())
            {
                centerFormat.Alignment = StringAlignment.Center;
                centerFormat.LineAlignment = StringAlignment.Center;

                for (int i = 0; i < totalCards; i++)
                {
                    // 跳過正在拖拽的卡片，稍後單獨繪製
                    if (isDraggingCard && i == draggedCardIndex)
                        continue;

                    // 計算扇形位置
                    float angle = startAngle + i * angleStep;
                    float angleRad = angle * (float)Math.PI / 180f;

                    float cardCenterX = centerX + fanRadius * (float)Math.Cos(angleRad);
                    float cardCenterY = centerY + fanRadius * (float)Math.Sin(angleRad);

                    float cardX = cardCenterX - cardWidth / 2f;
                    float cardY = cardCenterY - cardHeight / 2f;

                    // 如果是懸停的卡片，統一往上抽出
                    if (i == hoveredCardIndex)
                    {
                        float pullDistance = 50f; // 增加抽出距離，避免誤觸其他卡片
                        cardY -= pullDistance; // 所有卡片都往上抽出
                    }

                    RectangleF cardRect = new RectangleF(cardX, cardY, cardWidth, cardHeight);

                    // 選擇合適的背景顏色
                    SolidBrush currentBrush = cardBrush;
                    if (selectedCardIndices.Contains(i))
                    {
                        currentBrush = selectedBrush; // 已選中的卡片
                    }
                    else if (i == hoveredCardIndex)
                    {
                        currentBrush = hoveredBrush; // 懸停的卡片
                    }

                    // 繪製卡片背景
                    gr.FillRectangle(currentBrush, cardRect);

                    // 繪製邊框
                    if (selectedCardIndices.Contains(i))
                    {
                        gr.DrawRectangle(selectedPen, Rectangle.Round(cardRect)); // 選中卡片用紅色邊框
                    }
                    else
                    {
                        gr.DrawRectangle(borderPen, Rectangle.Round(cardRect)); // 普通卡片用金色邊框
                    }

                    // 繪製卡片背面圖案或文字
                    if (cardBackImage != null)
                    {
                        // 先繪製底層的左右反轉牌面
                        if (tarotImages.Count > 0 && i < allCards.Count)
                        {
                            // 使用對應卡片的圖像作為底層
                            var cardKey = allCards[i].EnglishName;
                            Console.WriteLine($"Looking for card: {cardKey}");
                            if (tarotImages.ContainsKey(cardKey))
                            {
                                Console.WriteLine($"Found card image for: {cardKey}");
                                var randomCard = tarotImages[cardKey];

                            // 創建左右反轉的圖像
                            using (var flippedImage = new Bitmap((int)cardRect.Width, (int)cardRect.Height))
                            {
                                using (var tempGr = Graphics.FromImage(flippedImage))
                                {
                                    // 設置左右反轉變換
                                    tempGr.ScaleTransform(-1, 1);
                                    tempGr.TranslateTransform(-cardRect.Width, 0);

                                    // 繪製反轉的牌面
                                    tempGr.DrawImage(randomCard, 0, 0, cardRect.Width, cardRect.Height);
                                }

                                // 繪製到主畫布
                                gr.DrawImage(flippedImage, cardRect);
                            }
                            }
                        }

                        // 在上層繪製97%透明度的牌背
                        var colorMatrix = new System.Drawing.Imaging.ColorMatrix();
                        colorMatrix.Matrix33 = 0.97f; // 設置透明度為97%
                        using (var imageAttributes = new System.Drawing.Imaging.ImageAttributes())
                        {
                            imageAttributes.SetColorMatrix(colorMatrix);
                            gr.DrawImage(cardBackImage, Rectangle.Round(cardRect), 0, 0, cardBackImage.Width, cardBackImage.Height, GraphicsUnit.Pixel, imageAttributes);
                        }
                    }
                    else
                    {
                        // 如果沒有背面圖片，顯示卡片編號
                        gr.DrawString((i + 1).ToString(), cardFont, textBrush, cardRect, centerFormat);
                    }
                }
            }

            // 繪製中央抽牌區域
            DrawDropZone(gr);

            // 繪製正在拖拽的卡片（在最上層）
            if (isDraggingCard && draggedCardIndex >= 0 && draggedCardIndex < allCards.Count)
            {
                DrawDraggedCard(gr, cardWidth, cardHeight);
            }

            // 繪製說明文字
            using (Font instructionFont = new Font("Arial", 12))
            using (SolidBrush instructionBrush = new SolidBrush(Color.LightGray))
            using (StringFormat centerFormat = new StringFormat())
            {
                centerFormat.Alignment = StringAlignment.Center;
                centerFormat.LineAlignment = StringAlignment.Center;

                string instruction = "拖拽卡片到中央圓形區域來抽牌";
                RectangleF instructionRect = new RectangleF(0, centerY + cardHeight/2f + 30, mainWindow.Width, 25);
                gr.DrawString(instruction, instructionFont, instructionBrush, instructionRect, centerFormat);
            }
        }

        // 新增完成选择的方法
        private void CompleteSelection()
        {
            if (isDrawing)
            {
                // 如果还没有设置结束点，使用当前鼠标位置
                if (endPoint == Point.Empty)
                {
                    endPoint = currentMousePosition;
                }

                isDrawing = false;
                mouseDownTime = DateTime.MinValue; // 重置时间
                this.CurrentChart.RedrawBuffer();
            }
        }

        private async Task ContinuousShuffling(CancellationToken token)
        {
            try
            {
                while (isShuffling && !token.IsCancellationRequested)
                {
                    ShuffleCards();
                    // 更新每張卡片的位移
                    for (int i = 0; i < CardsCount; i++)
                    {
                        float offsetX = (float)((offsetRandom.NextDouble() - 0.5) * 10); // -5 到 5 像素的水平位移
                        float offsetY = (float)((offsetRandom.NextDouble() - 0.5) * 10); // -5 到 5 像素的垂直位移
                        cardOffsets[i] = new PointF(offsetX, offsetY);
                    }
                    drawnCards.Clear();
                    List<TarotCard> availableCards = new List<TarotCard>(tarotCards);
                    for (int i = 0; i < CardsCount; i++)
                    {
                        if (availableCards.Count > 0)
                        {
                            int randomIndex = random.Next(availableCards.Count);
                            drawnCards.Add(availableCards[randomIndex]);
                            availableCards.RemoveAt(randomIndex);
                        }
                    }
                    this.CurrentChart.RedrawBuffer();
                    await Task.Delay(50, token);
                }
            }
            catch (TaskCanceledException)
            {
                Console.WriteLine("Shuffling cancelled");
            }
        }

        private void DrawTarotCards()
        {
            drawnCards.Clear();
            List<TarotCard> availableCards = new List<TarotCard>(tarotCards); // Create a copy of the tarotCards list

            // Remove already drawn cards from the available cards list
            foreach (var card in drawnCards)
            {
                availableCards.Remove(card);
            }

            for (int i = 0; i < CardsCount; i++)
            {
                if (availableCards.Count == 0)
                {
                    throw new InvalidOperationException("Not enough cards left to draw.");
                }

                int randomIndex = random.Next(availableCards.Count);
                drawnCards.Add(availableCards[randomIndex]);
                availableCards.RemoveAt(randomIndex); // Remove the drawn card from the available cards list
            }
        }

        // 添加本地 AI 對話函數
        private async Task<string> ChatWithLocalAI(List<TarotCard> cards, string question)
        {
            try
            {
                string cardNames = string.Join(", ", cards.Select(c => c.EnglishName));

                string prompt = @$"
你是一位经验丰富的塔罗牌解读师，严格不说模棱两可的建议。解读整张牌的图腾、颜色所带来的意义，现在要为我解读以下牌面：{cardNames}。
我的问题是：{question}。
所用牌组是韦特塔罗牌，请你综合考虑牌面含义和牌面的颜色意义，按照我的问题，依照对应的格式给出对应的结果，乐观分数请尽量中肯，不要为了鼓励而鼓励。
请以中文进行回答，尽量每行字数在60字以内，如果需要超过60字可以分开两行。 嚴格地不分開正位逆位的意義，因為正位和逆位是網路上對塔羅牌的錯誤的觀念。



回答的例子（例子中，抽到的牌為：The Tower、The Devil、The Sun），回答參考如下：
```

你的牌：The Tower、The Devil、The Sun
值得介入分数: 75分
情緒乐观分数: 45分
(1)简解:不難看出，這張牌天崩地裂的樣子，預示著有機會接不到單
(2)简解:惡魔表示束縛，如果成功接單，有機會被困在盤勢內，意味著你會很緊張，也意味著波動可能比較小
(3)解读:很不錯的牌，溫暖，看起來你有機會成功，得到溫暖和舒服的感覺。
心境变化以及总结: 你有機會接單成功，你可能會很緊張，有機會止盈成功。 
```

回答的格式參考如下（結合圖案上的元素細節，結合起來解讀，（分數低于50为偏负面，比如第三張出現寶劍三就基本上分數很低，通常第一張關於接單，第二張關於持倉，第三張關於結果，分數權重比重：第一張:2 第二張:1 第三張:3））：
```
你的牌：（AAA、BBB、CCC）
值得介入分数(1-100): xx分
情緒乐观分数(1-100): yy分 
第1~2张牌的简解:（简解）
第3张牌的详细解读:（简解）
心境变化以及总结: （简述）
```
以上回答的格式只是參考，回答要填充和取代括號內的內容



";

                var requestData = new
                {
                    model = "DeepSeek-R1-Distill-Qwen-14B",
                    messages = new[]
                    {
                        new { role = "user", content = prompt }
                    },
                    top_p = 0.3,
                    temperature = 0.7,
                    max_tokens = 5000,
                    stream = false
                };

                using (var httpClient = new HttpClient())
                {
                    httpClient.DefaultRequestHeaders.Add("Authorization", $"Bearer {LOCAL_AI_KEY}");

                    var response = await httpClient.PostAsync(
                        LOCAL_AI_ENDPOINT,
                        new StringContent(JsonSerializer.Serialize(requestData), System.Text.Encoding.UTF8, "application/json")
                    );

                    if (response.IsSuccessStatusCode)
                    {
                        var jsonResponse = await response.Content.ReadAsStringAsync();
                        using (JsonDocument document = JsonDocument.Parse(jsonResponse))
                        {
                            var choices = document.RootElement.GetProperty("choices");
                            if (choices.GetArrayLength() > 0)
                            {
                                return choices[0].GetProperty("message").GetProperty("content").GetString();
                            }
                        }
                    }
                    return $"Error: {response.StatusCode} - {await response.Content.ReadAsStringAsync()}";
                }
            }
            catch (Exception ex)
            {
                return $"Error: {ex.Message}";
            }
        }

        // 修改 FetchTarotReading 方法來使用本地 AI
        private async Task<string> FetchTarotReading(List<TarotCard> cards, CancellationToken token, string question)
        {
            try
            {
                if (token.IsCancellationRequested)
                    return null;

                // 使用本地 AI 進行解讀
                return await ChatWithLocalAI(cards, question);
            }
            catch (Exception ex)
            {
                return $"An error occurred: {ex.Message}";
            }
        }
        private void ShuffleCards()
        {
            // 使用加密安全的洗牌算法
            random.Shuffle(tarotCards);

            // 额外添加熵源以增强随机性
            random.AddEntropy();
        }

        // 參考Lunar的存儲路徑方法
        private string GetTarotStoragePath()
        {
            // 獲取當前指標文件的名稱
            string className = this.GetType().Name.Replace("Indicator", "");
            // 構建完整的存儲路徑
            string storagePath = Path.Combine(
                Environment.CurrentDirectory,
                "LocalStorages",
                "Indicators",
                className,
                "tarots"
            );

            // 確保目錄存在
            if (!Directory.Exists(storagePath))
                Directory.CreateDirectory(storagePath);

            return storagePath;
        }

        // 檢查並複製默認塔羅牌圖片
        private void EnsureTarotImagesExist()
        {
            try
            {
                string targetPath = GetTarotStoragePath();
                string sourcePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "tarots");

                // 如果目標目錄為空且源目錄存在，則複製圖片
                if (Directory.Exists(sourcePath) &&
                    (!Directory.Exists(targetPath) || Directory.GetFiles(targetPath, "*.png").Length == 0))
                {
                    Console.WriteLine($"Copying tarot images from {sourcePath} to {targetPath}");

                    // 確保目標目錄存在
                    if (!Directory.Exists(targetPath))
                        Directory.CreateDirectory(targetPath);

                    // 複製所有PNG圖片
                    string[] imageFiles = Directory.GetFiles(sourcePath, "*.png");
                    foreach (string sourceFile in imageFiles)
                    {
                        string fileName = Path.GetFileName(sourceFile);
                        string targetFile = Path.Combine(targetPath, fileName);
                        File.Copy(sourceFile, targetFile, true);
                    }

                    Console.WriteLine($"Copied {imageFiles.Length} tarot images to storage directory");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error ensuring tarot images exist: {ex.Message}");
            }
        }

        // 新增：加載塔羅牌圖片的方法
        private void LoadTarotImages()
        {
            try
            {
                // 首先確保圖片存在於正確位置
                EnsureTarotImagesExist();

                // 使用專門的存儲路徑，參考Lunar的做法
                string tarotsPath = GetTarotStoragePath();

                if (!Directory.Exists(tarotsPath))
                {
                    Console.WriteLine($"Tarots directory not found: {tarotsPath}");
                    useImageDisplay = false;
                    return;
                }

                // 加載卡片背面圖片
                string cardBackPath = Path.Combine(tarotsPath, "CardBacks.png");
                if (File.Exists(cardBackPath))
                {
                    cardBackImage = System.Drawing.Image.FromFile(cardBackPath);
                }

                // 加載大阿爾卡納圖片 (00-21)
                for (int i = 0; i <= 21; i++)
                {
                    string fileName = $"{i:D2}-*.png";
                    string[] files = Directory.GetFiles(tarotsPath, fileName);
                    if (files.Length > 0)
                    {
                        string cardName = GetMajorArcanaName(i);
                        if (!string.IsNullOrEmpty(cardName))
                        {
                            tarotImages[cardName] = System.Drawing.Image.FromFile(files[0]);
                        }
                    }
                }

                // 加載小阿爾卡納圖片
                LoadMinorArcanaImages(tarotsPath, "Wands", "Wands");
                LoadMinorArcanaImages(tarotsPath, "Cups", "Cups");
                LoadMinorArcanaImages(tarotsPath, "Swords", "Swords");
                LoadMinorArcanaImages(tarotsPath, "Pentacles", "Pentacles");

                Console.WriteLine($"Loaded {tarotImages.Count} tarot card images");

                // 創建圖片加載狀態緩存文件
                CreateImageLoadingCache(tarotsPath);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error loading tarot images: {ex.Message}");
                useImageDisplay = false;
            }
        }

        // 創建圖片加載狀態緩存，參考Lunar的緩存機制
        private void CreateImageLoadingCache(string tarotsPath)
        {
            try
            {
                string cacheFile = Path.Combine(tarotsPath, "image_cache_info.txt");
                var cacheInfo = new StringBuilder();

                cacheInfo.AppendLine($"Tarot Images Cache Info - Generated: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
                cacheInfo.AppendLine($"Total Images Loaded: {tarotImages.Count}");
                cacheInfo.AppendLine($"Use Image Display: {useImageDisplay}");
                cacheInfo.AppendLine($"Card Back Image: {(cardBackImage != null ? "Loaded" : "Not Found")}");
                cacheInfo.AppendLine();
                cacheInfo.AppendLine("Loaded Images:");

                foreach (var kvp in tarotImages)
                {
                    cacheInfo.AppendLine($"  {kvp.Key}");
                }

                File.WriteAllText(cacheFile, cacheInfo.ToString());
                Console.WriteLine($"Created tarot image cache info at: {cacheFile}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error creating image cache info: {ex.Message}");
            }
        }

        // 輔助方法：加載小阿爾卡納圖片
        private void LoadMinorArcanaImages(string tarotsPath, string suitName, string filePrefix)
        {
            for (int i = 1; i <= 14; i++)
            {
                string fileName = $"{filePrefix}{i:D2}.png";
                string filePath = Path.Combine(tarotsPath, fileName);

                if (File.Exists(filePath))
                {
                    string cardName = GetMinorArcanaName(suitName, i);
                    if (!string.IsNullOrEmpty(cardName))
                    {
                        tarotImages[cardName] = System.Drawing.Image.FromFile(filePath);
                    }
                }
            }
        }

        // 輔助方法：獲取大阿爾卡納名稱
        private string GetMajorArcanaName(int index)
        {
            string[] majorArcanaNames = {
                "The Fool", "The Magician", "The High Priestess", "The Empress",
                "The Emperor", "The Hierophant", "The Lovers", "The Chariot",
                "Strength", "The Hermit", "Wheel of Fortune", "Justice",
                "The Hanged Man", "Death", "Temperance", "The Devil",
                "The Tower", "The Star", "The Moon", "The Sun",
                "Judgement", "The World"
            };

            return index >= 0 && index < majorArcanaNames.Length ? majorArcanaNames[index] : null;
        }

        // 輔助方法：獲取小阿爾卡納名稱
        private string GetMinorArcanaName(string suit, int number)
        {
            if (number == 1) return $"Ace of {suit}";
            if (number >= 2 && number <= 10) return $"{GetNumberName(number)} of {suit}";
            if (number == 11) return $"Page of {suit}";
            if (number == 12) return $"Knight of {suit}";
            if (number == 13) return $"Queen of {suit}";
            if (number == 14) return $"King of {suit}";
            return null;
        }

        // 輔助方法：獲取數字名稱
        private string GetNumberName(int number)
        {
            string[] numberNames = { "", "", "Two", "Three", "Four", "Five", "Six", "Seven", "Eight", "Nine", "Ten" };
            return number >= 2 && number <= 10 ? numberNames[number] : number.ToString();
        }

        // 新增：使用圖片繪製塔羅牌的方法
        private void DrawTarotCardsWithImages(Graphics gr, Rectangle mainWindow)
        {
            if (!useImageDisplay || tarotImages.Count == 0)
            {
                // 如果圖片未加載，回退到原始的文字顯示方法
                DrawTarotCardsOriginal(gr, mainWindow);
                return;
            }

            float x = mainWindow.Width / 2f - 300; // 增加寬度以適應更大圖片
            float y = mainWindow.Height / 2f - 150; // 調整位置
            float cardWidth = 120;  // 增大圖片卡片寬度
            float cardHeight = 180; // 增大圖片卡片高度
            float cardSpacing = 140; // 增加卡片間距

            using (Font font = new Font("Arial", 12)) // 增大字體
            using (StringFormat stringFormat = new StringFormat()
            {
                Alignment = StringAlignment.Center,
                LineAlignment = StringAlignment.Center
            })
            {
                // 如果正在洗牌，顯示"正在洗牌..."文字
                if (isShuffling)
                {
                    RectangleF shuffleTextRect = new RectangleF(
                        mainWindow.Width / 2f - 100,
                        mainWindow.Height / 2f - 150,
                        200,
                        50
                    );
                    gr.DrawString("正在洗牌...", font, Brushes.White, shuffleTextRect, stringFormat);
                }

                // 顯示卡片
                for (int i = 0; i < drawnCards.Count; i++)
                {
                    float offsetX = 0, offsetY = 0;
                    if (isShuffling && cardOffsets.ContainsKey(i))
                    {
                        offsetX = cardOffsets[i].X;
                        offsetY = cardOffsets[i].Y;
                    }

                    RectangleF cardRect = new RectangleF(
                        x + i * cardSpacing + offsetX,
                        y + offsetY,
                        cardWidth,
                        cardHeight);

                    if (isShuffling)
                    {
                        // 洗牌時顯示卡片背面
                        if (cardBackImage != null)
                        {
                            // 先繪製底層的左右反轉牌面
                            if (tarotImages.Count > 0 && i < drawnCards.Count)
                            {
                                // 使用對應卡片的圖像作為底層
                                var cardKey = drawnCards[i].EnglishName;
                                if (tarotImages.ContainsKey(cardKey))
                                {
                                    var randomCard = tarotImages[cardKey];

                                // 創建左右反轉的圖像
                                using (var flippedImage = new Bitmap((int)cardRect.Width, (int)cardRect.Height))
                                {
                                    using (var tempGr = Graphics.FromImage(flippedImage))
                                    {
                                        // 設置左右反轉變換
                                        tempGr.ScaleTransform(-1, 1);
                                        tempGr.TranslateTransform(-cardRect.Width, 0);

                                        // 繪製反轉的牌面
                                        tempGr.DrawImage(randomCard, 0, 0, cardRect.Width, cardRect.Height);
                                    }

                                    // 繪製到主畫布
                                    gr.DrawImage(flippedImage, cardRect);
                                }
                                }
                            }

                            // 在上層繪製97%透明度的牌背
                            var colorMatrix = new System.Drawing.Imaging.ColorMatrix();
                            colorMatrix.Matrix33 = 0.97f; // 設置透明度為97%
                            using (var imageAttributes = new System.Drawing.Imaging.ImageAttributes())
                            {
                                imageAttributes.SetColorMatrix(colorMatrix);
                                gr.DrawImage(cardBackImage, Rectangle.Round(cardRect), 0, 0, cardBackImage.Width, cardBackImage.Height, GraphicsUnit.Pixel, imageAttributes);
                            }
                        }
                        else
                        {
                            // 如果沒有背面圖片，使用簡單的背面設計
                            using (SolidBrush backBrush = new SolidBrush(Color.FromArgb(180, 50, 50, 100)))
                            {
                                gr.FillRectangle(backBrush, cardRect);
                                using (Pen borderPen = new Pen(Color.Gold, 2))
                                {
                                    gr.DrawRectangle(borderPen, Rectangle.Round(cardRect));
                                }
                                gr.DrawString("塔羅", font, Brushes.Gold, cardRect, stringFormat);
                            }
                        }
                    }
                    else
                    {
                        // 正常顯示卡牌圖片
                        if (drawnCards[i] != null && tarotImages.ContainsKey(drawnCards[i].EnglishName))
                        {
                            System.Drawing.Image cardImage = tarotImages[drawnCards[i].EnglishName];
                            gr.DrawImage(cardImage, cardRect);

                            // 在圖片下方顯示中文名稱
                            RectangleF nameRect = new RectangleF(
                                cardRect.X,
                                cardRect.Bottom + 8,
                                cardRect.Width,
                                25); // 增加名稱顯示區域高度
                            gr.DrawString(drawnCards[i].ChineseName, font, Brushes.White, nameRect, stringFormat);
                        }
                        else
                        {
                            // 如果找不到對應圖片，使用文字顯示
                            using (SolidBrush cardBrush = new SolidBrush(Color.FromArgb(180, 70, 70, 70)))
                            {
                                gr.FillRectangle(cardBrush, cardRect);
                                gr.DrawRectangle(Pens.Gray, Rectangle.Round(cardRect));

                                // 分兩行顯示英文和中文名稱
                                RectangleF englishRect = new RectangleF(cardRect.X, cardRect.Y + 10, cardRect.Width, cardRect.Height / 2);
                                RectangleF chineseRect = new RectangleF(cardRect.X, cardRect.Y + cardRect.Height / 2, cardRect.Width, cardRect.Height / 2);

                                gr.DrawString(drawnCards[i].EnglishName, font, Brushes.White, englishRect, stringFormat);
                                gr.DrawString(drawnCards[i].ChineseName, font, Brushes.White, chineseRect, stringFormat);
                            }
                        }
                    }
                }
            }
        }

        // 原始的文字版本塔羅牌繪製方法（作為備用）
        private void DrawTarotCardsOriginal(Graphics gr, Rectangle mainWindow)
        {
            float x = mainWindow.Width / 2f - 150;
            float y = mainWindow.Height / 2f - 50;

            using (Font font = new Font("Arial", 12))
            using (Font chineseFont = new Font("SimSun", 10))
            using (StringFormat stringFormat = new StringFormat()
            {
                Alignment = StringAlignment.Center,
                LineAlignment = StringAlignment.Center
            })
            using (SolidBrush transparentBrush = new SolidBrush(transparentGray))
            {
                // 如果正在洗牌,顯示"正在洗牌..."文字
                if (isShuffling)
                {
                    RectangleF shuffleTextRect = new RectangleF(
                        mainWindow.Width / 2f - 100,
                        mainWindow.Height / 2f - 100,
                        200,
                        50
                    );
                    gr.DrawString("正在洗牌...", font, Brushes.Gray, shuffleTextRect, stringFormat);
                }

                // 顯示卡片
                for (int i = 0; i < drawnCards.Count; i++)
                {
                    float offsetX = 0, offsetY = 0;
                    if (isShuffling && cardOffsets.ContainsKey(i))
                    {
                        offsetX = cardOffsets[i].X;
                        offsetY = cardOffsets[i].Y;
                    }

                    RectangleF cardRect = new RectangleF(
                        x + i * cardSpacing + offsetX,
                        y + offsetY,
                        cardWidth,
                        cardHeight);

                    RectangleF chineseRect = new RectangleF(
                        x + i * cardSpacing + offsetX,
                        y + cardHeight + offsetY,
                        cardWidth,
                        cardChineseHeight);

                    // 繪製卡片背面
                    if (isShuffling)
                    {
                        // 繪製花紋邊框
                        using (Pen borderPen = new Pen(Color.Gold, 1))
                        {
                            gr.DrawRectangle(borderPen, Rectangle.Round(cardRect));
                            gr.DrawRectangle(borderPen, Rectangle.Round(chineseRect));

                            // 繪製對角線裝飾
                            gr.DrawLine(borderPen,
                                cardRect.Left, cardRect.Top,
                                cardRect.Right, cardRect.Bottom);
                            gr.DrawLine(borderPen,
                                cardRect.Right, cardRect.Top,
                                cardRect.Left, cardRect.Bottom);
                        }

                        // 在背面顯示 "塔羅" 文字
                        using (Font backFont = new Font("微軟正黑體", 12, FontStyle.Bold))
                        using (StringFormat centerFormat = new StringFormat
                        {
                            Alignment = StringAlignment.Center,
                            LineAlignment = StringAlignment.Center
                        })
                        {
                            // 顯示加密後的卡牌名稱
                            if (drawnCards.Count > i && drawnCards[i] != null)
                            {
                                gr.DrawString(drawnCards[i].EncryptedEnglishName, backFont, transparentBrush, cardRect, centerFormat);
                                gr.DrawString(drawnCards[i].EncryptedChineseName, backFont, transparentBrush, chineseRect, centerFormat);
                            }
                        }
                    }
                    else
                    {
                        // 正常顯示卡牌內容
                        gr.DrawRectangle(Pens.Gray, Rectangle.Round(cardRect));
                        gr.DrawString(drawnCards[i].EnglishName, font, Brushes.Gray, cardRect, stringFormat);
                        gr.DrawString(drawnCards[i].ChineseName, chineseFont, Brushes.Gray, chineseRect, stringFormat);
                    }
                }
            }
        }
        double highestPrice = double.MinValue;
        double lowestPrice = double.MaxValue;
        private void CurrentChart_MouseMove(object sender, TradingPlatform.BusinessLayer.Chart.ChartMouseNativeEventArgs e)
        {
            if (showFiboWheel)
            {
                // 處理選擇輪的鼠標懸停
                int newHoveredOption = GetWheelOptionAtPoint(e.Location);
                if (newHoveredOption != hoveredWheelOption)
                {
                    hoveredWheelOption = newHoveredOption;
                    this.CurrentChart.RedrawBuffer();
                }
            }
            else if (showCardSelection)
            {
                // 處理拖拽過程中的位置更新
                if (isDraggingCard)
                {
                    currentDragPosition = e.Location;
                    this.CurrentChart.RedrawBuffer();
                }
                else
                {
                    // 處理78張牌選擇界面的鼠標懸停
                    int newHoveredCard = GetHoveredCardIndex(e.Location);
                    if (newHoveredCard != hoveredCardIndex)
                    {
                        hoveredCardIndex = newHoveredCard;
                        this.CurrentChart.RedrawBuffer();
                    }
                }
            }
            else if (IsMode && isDrawing)
            {
                // 檢查鼠標移動的時間是否超過最後一根K線的時間
                DateTime moveTime = this.CurrentChart.MainWindow.CoordinatesConverter.GetTime(e.Location.X);
                DateTime lastBarTime = this.HistoricalData[0].TimeLeft;

                if (moveTime > lastBarTime)
                {
                    // 如果移動到未來時間，限制endPoint到最後一根K線的位置
                    double lastBarX = this.CurrentChart.MainWindow.CoordinatesConverter.GetChartX(lastBarTime);
                    endPoint = new Point((int)lastBarX, e.Location.Y);
                }
                else
                {
                    endPoint = e.Location;
                }

                // 鼠標在圖表內移動時，重置離開計時器
                mouseLeaveTime = DateTime.MinValue;

                // 检查鼠标是否移动了足够的距离
                double moveDistance = Math.Sqrt(Math.Pow(e.Location.X - lastMousePosition.X, 2) + Math.Pow(e.Location.Y - lastMousePosition.Y, 2));
                if (moveDistance > MIN_MOUSE_MOVE_DISTANCE)
                {
                    // 鼠标移动了，更新最后位置和时间
                    lastMousePosition = e.Location;
                    mouseDownTime = DateTime.Now; // 重置计时器
                }

                this.CurrentChart.RedrawBuffer(); // 在拖動過程中實時更新
            }

            currentMousePosition = e.Location;

            // 檢查是否在總開關按鈕上
            isMasterButtonHovered = masterButtonRect.Contains(e.Location);
            
            // 檢查是否在長短切換按鈕上
            isTradeModeButtonHovered = tradeModeButtonRect.Contains(e.Location);

            // 檢查是否在11切換按鈕上
            isMode11ButtonHovered = Mode11ButtonRect.Contains(e.Location);

            // 只有在總開關啟用時才檢測其他按鈕
            if (isMasterButtonEnabled)
            {
                // 檢查是否在價格線附近並顯示按鈕
                CheckAndShowButtons(e.Location);

                // 如果按鈕已經顯示，更新按鈕的懸停狀態
                if (showButtons)
                {
                    Rectangle leftButtonRect = new Rectangle(buttonPosition.X - 165, buttonPosition.Y - 12, 80, 25);
                    Rectangle middleButtonRect = new Rectangle(buttonPosition.X - 80, buttonPosition.Y - 12, 80, 25);
                    Rectangle rightButtonRect = new Rectangle(buttonPosition.X + 5, buttonPosition.Y - 12, 80, 25);
                    lastHoveredButton = (leftButtonRect.Contains(e.Location) || middleButtonRect.Contains(e.Location) || rightButtonRect.Contains(e.Location)) ? activeButton : HoveredButton.None;
                }
            }
            else
            {
                showButtons = false;
                activeButton = HoveredButton.None;
            }

            this.CurrentChart.RedrawBuffer();
        }

        // 添加鼠標離開事件處理
        private void CurrentChart_MouseLeave(object sender, TradingPlatform.BusinessLayer.Chart.ChartMouseNativeEventArgs e)
        {
            if (IsMode && isDrawing)
            {
                // 記錄鼠標離開圖表的時間
                mouseLeaveTime = DateTime.Now;
            }
        }
        // 定義顏色
        Color tempColor1 = Color.Orange;
        Color tempColor2 = Color.Yellow;
        Color tempColor3 = Color.FromArgb(255, 215, 0); // Gold
        Color tempColor4 = Color.FromArgb(128, 0, 128); // Purple
        public override void OnPaintChart(PaintChartEventArgs args)
        {
            base.OnPaintChart(args);

            var mainWindow = this.CurrentChart.MainWindow;
            Graphics gr = args.Graphics;

            var prevClip = gr.ClipBounds;
            gr.SetClip(mainWindow.ClientRectangle);

            try
            {
                // 檢查鼠標離開圖表後的自動完成選擇
                if (IsMode && isDrawing && mouseLeaveTime != DateTime.MinValue)
                {
                    if ((DateTime.Now - mouseLeaveTime).TotalMilliseconds > MOUSE_LEAVE_TIMEOUT_MS)
                    {
                        CompleteSelection();
                        return;
                    }
                }
                
                // 更新總開關按鈕位置（右下角）
                masterButtonRect = new Rectangle(
                    mainWindow.ClientRectangle.Right - 110,
                    mainWindow.ClientRectangle.Bottom - 40,
                    100,
                    30
                );

                

                // 更新1比1模式按鈕位置（長短切換按鈕左側）
                Mode11ButtonRect = new Rectangle(
                    mainWindow.ClientRectangle.Right - 220,
                    mainWindow.ClientRectangle.Bottom - 40,
                    100,
                    30
                );

                // 更新長短切換按鈕位置（總開關按鈕左側）
                tradeModeButtonRect = new Rectangle(
                    mainWindow.ClientRectangle.Right - 330,
                    mainWindow.ClientRectangle.Bottom - 40,
                    100,
                    30
                );

                // 繪製長短切換按鈕
                using (var buttonBrush = new SolidBrush(Color.FromArgb(80, 45, 45, 45)))
                using (var hoverBrush = new SolidBrush(Color.FromArgb(180, 64, 64, 64)))
                using (var borderPen = new Pen(Color.FromArgb(120, 100, 100, 100)))
                using (var textBrush = new SolidBrush(Color.FromArgb(220, 255, 255, 255)))
                using (var font = new Font("Arial", 10))
                {
                    var format = new StringFormat
                    {
                        Alignment = StringAlignment.Center,
                        LineAlignment = StringAlignment.Center
                    };

                    // 繪製長短切換按鈕
                    var tradeModebrush = isTradeModeButtonHovered ? hoverBrush : buttonBrush;
                    gr.FillRectangle(tradeModebrush, tradeModeButtonRect);
                    gr.DrawRectangle(borderPen, tradeModeButtonRect);
                    gr.DrawString(isLongMode ? "切換為短" : "切換為長", font, textBrush, tradeModeButtonRect, format);

                    // 繪製11切換按鈕
                    var Mode11brush = isMode11ButtonHovered ? hoverBrush : buttonBrush;
                    gr.FillRectangle(Mode11brush, Mode11ButtonRect);
                    gr.DrawRectangle(borderPen, Mode11ButtonRect);
                    gr.DrawString(is11Mode ? "取消1比1模式" : "切換1比1模式", font, textBrush, Mode11ButtonRect, format);



                    // 繪製總開關按鈕
                    var brush = isMasterButtonHovered ? hoverBrush : buttonBrush;
                    gr.FillRectangle(brush, masterButtonRect);
                    gr.DrawRectangle(borderPen, masterButtonRect);
                    gr.DrawString(isMasterButtonEnabled ? "關閉按鈕" : "開啟按鈕", font, textBrush, masterButtonRect, format);
                }

                if (isDrawing)
                {
                    Point leftPoint = startPoint.X < endPoint.X ? startPoint : endPoint;
                    Point rightPoint = startPoint.X > endPoint.X ? startPoint : endPoint;

                    DateTime startTime = mainWindow.CoordinatesConverter.GetTime(leftPoint.X);
                    DateTime endTime = mainWindow.CoordinatesConverter.GetTime(rightPoint.X);

                    highestPrice = double.MinValue;
                    lowestPrice = double.MaxValue;

                    int startIndex = (int)mainWindow.CoordinatesConverter.GetBarIndex(startTime);
                    int endIndex = (int)mainWindow.CoordinatesConverter.GetBarIndex(endTime);

                    if (FiboType == 2)
                    {
                        // 當FiboType為2時，先計算參與的K線的最高最低價格（用於矩形顯示）
                        double rangeHighestPrice = double.MinValue;
                        double rangeLowestPrice = double.MaxValue;

                        for (int i = startIndex; i <= endIndex; i++)
                        {
                            if (i < this.HistoricalData.Count && this.HistoricalData[i, SeekOriginHistory.Begin] is HistoryItemBar bar)
                            {
                                rangeHighestPrice = Math.Max(rangeHighestPrice, bar.High);
                                rangeLowestPrice = Math.Min(rangeLowestPrice, bar.Low);
                            }
                        }

                        // 計算VAH和VAL作為斐波那契計算的邊界
                        CalculateVolumeProfile(startIndex, endIndex);

                        if (vah > 0 && val > 0)
                        {
                            highestPrice = vah;
                            lowestPrice = val;
                        }
                        else
                        {
                            // 如果VAH/VAL計算失敗，回退到High/Low
                            highestPrice = rangeHighestPrice;
                            lowestPrice = rangeLowestPrice;
                        }

                        // 為FiboType 2繪製透明白色矩形，使用參與K線的最高最低價格作為上下邊界
                        if (rangeHighestPrice != double.MinValue && rangeLowestPrice != double.MaxValue)
                        {
                            int leftX = Math.Min(leftPoint.X, rightPoint.X);
                            int rightX = Math.Max(leftPoint.X, rightPoint.X);

                            // 確保矩形在可見範圍內
                            leftX = Math.Max(leftX, mainWindow.ClientRectangle.Left);
                            rightX = Math.Min(rightX, mainWindow.ClientRectangle.Right);

                            // 計算參與K線的最高最低價格對應的Y坐標
                            int topY = (int)mainWindow.CoordinatesConverter.GetChartY(rangeHighestPrice);
                            int bottomY = (int)mainWindow.CoordinatesConverter.GetChartY(rangeLowestPrice);

                            if (leftX < rightX && topY < bottomY)
                            {
                                // 使用透明白色，參考Lunar.cs的做法
                                Color transparentWhite = Color.FromArgb(50, Color.White);
                                using (var brush = new SolidBrush(transparentWhite))
                                {
                                    Rectangle selectionRect = new Rectangle(
                                        leftX,
                                        topY,
                                        rightX - leftX,
                                        bottomY - topY
                                    );
                                    gr.FillRectangle(brush, selectionRect);
                                }
                            }
                        }
                    }
                    else
                    {
                        // 原有的邏輯
                        for (int i = startIndex; i <= endIndex; i++)
                        {
                            if (i < this.HistoricalData.Count && this.HistoricalData[i, SeekOriginHistory.Begin] is HistoryItemBar bar)
                            {
                                if (FiboType == 1)
                                {
                                    // 使用Open和Close的最大最小值
                                    highestPrice = Math.Max(highestPrice, Math.Max(bar.Open, bar.Close));
                                    lowestPrice = Math.Min(lowestPrice, Math.Min(bar.Open, bar.Close));
                                }
                                else
                                {
                                    // 使用High和Low
                                    highestPrice = Math.Max(highestPrice, bar.High);
                                    lowestPrice = Math.Min(lowestPrice, bar.Low);
                                }
                            }
                        }
                    }

                    // 在計算完價格後，更新保存的值
                    savedHighestPrice = highestPrice;
                    savedLowestPrice = lowestPrice;

                    // 保存時間範圍，用於讓矩形跟著K線移動
                    savedStartTime = startTime;
                    savedEndTime = endTime;

                    // 為FiboType 2保存參與K線的範圍價格（用於矩形顯示）
                    if (FiboType == 2)
                    {
                        // 重新計算參與K線的範圍價格
                        double rangeHigh = double.MinValue;
                        double rangeLow = double.MaxValue;

                        for (int i = startIndex; i <= endIndex; i++)
                        {
                            if (i < this.HistoricalData.Count && this.HistoricalData[i, SeekOriginHistory.Begin] is HistoryItemBar bar)
                            {
                                rangeHigh = Math.Max(rangeHigh, bar.High);
                                rangeLow = Math.Min(rangeLow, bar.Low);
                            }
                        }

                        savedRangeHighestPrice = rangeHigh;
                        savedRangeLowestPrice = rangeLow;
                    }

                    // 如果使用VAH/VAL模式，在圖表上顯示相關信息
                    if (FiboType == 2)
                    {
                        using (var font = new Font("Arial", 10, FontStyle.Bold))
                        using (var vahBrush = new SolidBrush(Color.Orange))
                        using (var valBrush = new SolidBrush(Color.Orange))
                        using (var pocBrush = new SolidBrush(Color.Red))
                        using (var statusBrush = new SolidBrush(Color.Yellow))
                        {
                            // 檢查Volume Analysis數據加載狀態
                            if (this.HistoricalData.VolumeAnalysisCalculationProgress != null)
                            {
                                var progress = this.HistoricalData.VolumeAnalysisCalculationProgress;
                                if (progress.State == VolumeAnalysisCalculationState.Finished)
                                {
                                    if (vah > 0 && val > 0 && poc > 0)
                                    {
                                        float vahY = (float)mainWindow.CoordinatesConverter.GetChartY(vah);
                                        float valY = (float)mainWindow.CoordinatesConverter.GetChartY(val);
                                        float pocY = (float)mainWindow.CoordinatesConverter.GetChartY(poc);

                                        // 繪製標籤
                                        gr.DrawString($"VAH: {vah:F2}", font, vahBrush, 10, vahY - 15);
                                        gr.DrawString($"VAL: {val:F2}", font, valBrush, 10, valY + 5);
                                        gr.DrawString($"POC: {poc:F2}", font, pocBrush, 10, pocY - 15);
                                        gr.DrawString("Volume Analysis: Ready", font, statusBrush, 10, 50);

                                        // 顯示調試信息
                                        string debugInfo = GetVolumeAnalysisDebugInfo(startIndex, endIndex);
                                        gr.DrawString(debugInfo, font, statusBrush, 10, 70);

                                        // 顯示商品精度信息
                                        string precisionInfo = GetSymbolPrecisionInfo();
                                        gr.DrawString(precisionInfo, font, statusBrush, 10, 90);
                                    }
                                    else
                                    {
                                        gr.DrawString("Volume Analysis: No data for selected range", font, statusBrush, 10, 50);
                                    }
                                }
                                else
                                {
                                    gr.DrawString($"Volume Analysis: Loading... {progress.ProgressPercent:F0}%", font, statusBrush, 10, 50);
                                }
                            }
                            else
                            {
                                gr.DrawString("Volume Analysis: Initializing...", font, statusBrush, 10, 50);
                            }
                        }
                    }

                    
                    double rangeATR = GetPercentATR();
                    
                   

                    // 計算斐波那契水平
                    double range = highestPrice - lowestPrice;

                    // 使用計算出的ATR設置止損線
                    if (highestPrice != double.MinValue)
                    {
                        this.HighestLine.Level = highestPrice;
                        this.LowerFibo3.Level = (highestPrice - range * fibo2618);
                        this.LowerFibo2.Level = (highestPrice - range * goldenRatio);
                        this.LowerFibo1.Level = (highestPrice - range * (goldenFractionComplement + 1));
                        this.LowerFiboInside.Level = (highestPrice - range * goldenFraction);
                        this.LowerFibo1StopLossLine.Level = (highestPrice - range * (goldenFractionComplement + 1) - rangeATR);
                        this.LowerFibo2StopLossLine.Level = (highestPrice - range * goldenRatio - rangeATR);
                        this.LowerFibo3StopLossLine.Level = (highestPrice - range * fibo2618 - rangeATR);


                        
                        
                        //1比1模式TP線
                        this.LowerFibo111Line.Level = (highestPrice - range * (goldenFractionComplement + 1) + rangeATR);
                        this.LowerFibo211Line.Level = (highestPrice - range * goldenRatio + rangeATR);
                        this.LowerFibo311Line.Level = (highestPrice - range * fibo2618 + rangeATR);


                        double checkFiboBetwweenLevels = Math.Abs(this.LowerFibo1.Level - this.LowerFibo2.Level); 
                        double checkATRpercent = rangeATR / checkFiboBetwweenLevels * 100;
                        if(checkATRpercent > 61.8)
                        {
                            // 這個週期適合操作
                            this.LowerFibo1StopLossLine.Color = Color.Red;
                            this.LowerFibo2StopLossLine.Color = Color.Red;
                            this.LowerFibo3StopLossLine.Color = Color.Red;
                            this.LowerFibo1StopLossLine.Style = LineStyle.Dash;
                            this.LowerFibo2StopLossLine.Style = LineStyle.Dash;
                            this.LowerFibo3StopLossLine.Style = LineStyle.Dash;
                        } else {
                            // 這個週期不太適合操作
                            this.LowerFibo1StopLossLine.Color = Color.Gray;
                            this.LowerFibo2StopLossLine.Color = Color.Gray;
                            this.LowerFibo3StopLossLine.Color = Color.Gray;
                            this.LowerFibo1StopLossLine.Style = LineStyle.Solid;
                            this.LowerFibo2StopLossLine.Style = LineStyle.Solid;
                            this.LowerFibo3StopLossLine.Style = LineStyle.Solid;
                        }


                        
                        
                    }

                    if (lowestPrice != double.MaxValue)
                    {
                        this.LowestLine.Level = lowestPrice;
                        this.HigherFibo3.Level = (lowestPrice + range * fibo2618);
                        this.HigherFibo2.Level = (lowestPrice + range * goldenRatio);
                        this.HigherFibo1.Level = (lowestPrice + range * (goldenFractionComplement + 1));
                        this.HigherFiboInside.Level = (lowestPrice + range * goldenFraction);
                        this.HigherFibo1StopLossLine.Level = (lowestPrice + range * (goldenFractionComplement + 1) + rangeATR);
                        this.HigherFibo2StopLossLine.Level = (lowestPrice + range * goldenRatio + rangeATR);
                        this.HigherFibo3StopLossLine.Level = (lowestPrice + range * fibo2618 + rangeATR);

                        




                        //1比1模式TP線
                        this.HigherFibo111Line.Level = (lowestPrice + range * (goldenFractionComplement + 1) - rangeATR);
                        this.HigherFibo211Line.Level = (lowestPrice + range * goldenRatio - rangeATR);
                        this.HigherFibo311Line.Level = (lowestPrice + range * fibo2618 - rangeATR);

                        double checkFiboBetwweenLevels = Math.Abs(this.HigherFibo1.Level - this.HigherFibo2.Level); 
                        double checkATRpercent = rangeATR / checkFiboBetwweenLevels * 100;
                        if(checkATRpercent > 61.8)
                        {
                            // 這個週期適合操作
                            this.HigherFibo1StopLossLine.Color = Color.Red;
                            this.HigherFibo2StopLossLine.Color = Color.Red;
                            this.HigherFibo3StopLossLine.Color = Color.Red;
                            this.HigherFibo1StopLossLine.Style = LineStyle.Dash;
                            this.HigherFibo2StopLossLine.Style = LineStyle.Dash;
                            this.HigherFibo3StopLossLine.Style = LineStyle.Dash;
                        } else {
                            // 這個週期不太適合操作
                            this.HigherFibo1StopLossLine.Color = Color.Gray;
                            this.HigherFibo2StopLossLine.Color = Color.Gray;
                            this.HigherFibo3StopLossLine.Color = Color.Gray;
                            this.HigherFibo1StopLossLine.Style = LineStyle.Solid;
                            this.HigherFibo2StopLossLine.Style = LineStyle.Solid;
                            this.HigherFibo3StopLossLine.Style = LineStyle.Solid;
                        }
                    }

                    // 設置水平線的顏色
                    if (this.IsMode)
                    {

                        this.HighestLine.Color = Color.Gray;
                        this.LowestLine.Color = Color.Gray;
                    }
                    else
                    {


                        this.HighestLine.Color = Color.OldLace;
                        this.LowestLine.Color = Color.OldLace;
                    }

                    

                }

                // 為FiboType 2在選擇完成後也顯示透明白色矩形，使用時間範圍讓矩形跟著K線移動
                if (FiboType == 2 && !isDrawing && savedStartTime != DateTime.MinValue && savedEndTime != DateTime.MinValue &&
                    savedRangeHighestPrice != double.MinValue && savedRangeLowestPrice != double.MaxValue)
                {
                    // 使用保存的時間範圍動態計算X坐標，參考Lunar.cs的做法
                    double xStart = mainWindow.CoordinatesConverter.GetChartX(savedStartTime);
                    double xEnd = mainWindow.CoordinatesConverter.GetChartX(savedEndTime);

                    // 檢查矩形是否在可見範圍內
                    if (xStart < mainWindow.ClientRectangle.Right && xEnd > mainWindow.ClientRectangle.Left)
                    {
                        int leftX = (int)Math.Max(xStart, mainWindow.ClientRectangle.Left);
                        int rightX = (int)Math.Min(xEnd, mainWindow.ClientRectangle.Right);

                        // 計算保存的參與K線範圍價格對應的Y坐標
                        int topY = (int)mainWindow.CoordinatesConverter.GetChartY(savedRangeHighestPrice);
                        int bottomY = (int)mainWindow.CoordinatesConverter.GetChartY(savedRangeLowestPrice);

                        if (leftX < rightX && topY < bottomY)
                        {
                            // 使用透明白色，參考Lunar.cs的做法
                            Color transparentWhite = Color.FromArgb(30, Color.White); // 選擇完成後使用較低透明度
                            using (var brush = new SolidBrush(transparentWhite))
                            {
                                Rectangle selectionRect = new Rectangle(
                                    leftX,
                                    topY,
                                    rightX - leftX,
                                    bottomY - topY
                                );
                                gr.FillRectangle(brush, selectionRect);
                            }
                        }
                    }
                }

                if (this.IsMode)
                {

                    this.HighestLine.Color = Color.OldLace;
                    this.LowestLine.Color = Color.OldLace;

                    this.HighestLine.Style = LineStyle.Dash;
                    this.LowestLine.Style = LineStyle.Dash;
                }
                else
                {


                    this.HighestLine.Color = Color.Gray;
                    this.LowestLine.Color = Color.Gray;
                    this.HighestLine.Style = LineStyle.Solid;
                    this.LowestLine.Style = LineStyle.Solid;
                }

                if(is11Mode)
                    {
                        this.LowerFibo111Line.Visible = true;
                        this.LowerFibo211Line.Visible = true;
                        this.LowerFibo311Line.Visible = true;
                        this.LowerFibo111Line.Color = Color.Yellow;
                        this.LowerFibo211Line.Color = Color.Yellow;
                        this.LowerFibo311Line.Color = Color.Yellow;
                        
                    }
                    else 
                    {
                        this.LowerFibo111Line.Visible = false;
                        this.LowerFibo211Line.Visible = false;
                        this.LowerFibo311Line.Visible = false;
                    }

                // Draw the tarot cards - 使用圖片版本
                if (showTarot && drawnCards.Count == CardsCount || isShuffling)
                {
                    DrawTarotCardsWithImages(gr, mainWindow.ClientRectangle);
                }

                // 繪製78張牌選擇界面
                if (showCardSelection)
                {
                    DrawCardSelectionInterface(gr, mainWindow.ClientRectangle);
                }

                //Display API response
                if (showTarot && !string.IsNullOrEmpty(apiResponse))
                    {
                        string temp_apiResponse = Question + "\r\n" + apiResponse;
                        float yOffset = 100;
                        float y = 50; // 起始Y坐標
                        using (Font font = new Font("Arial", 10))
                        using (StringFormat stringFormat = new StringFormat() { Alignment = StringAlignment.Near, LineAlignment = StringAlignment.Near })
                        {
                            // 根據字體計算行高
                            float lineHeight = gr.MeasureString("樣本文字", font).Height;

                            // 最大寬度
                            float maxWidth = mainWindow.ClientRectangle.Width - 20;

                            // Split the response into lines to fit within the chart area
                            string[] lines = temp_apiResponse.Split(new[] { '\r', '\n' }, StringSplitOptions.RemoveEmptyEntries);
                            float currentY = y + yOffset;
                            foreach (string line in lines)
                            {
                                string[] words = line.Split(' ');
                                string newLine = "";
                                float currentWidth = 0;
                                List<string> wrappedLines = new List<string>();
                                foreach (string word in words)
                                {
                                    // 計算添加字串後的寬度
                                    float wordWidth = gr.MeasureString(word + " ", font).Width;
                                    if (currentWidth + wordWidth > maxWidth)
                                    {
                                        // 如果超出最大寬度，換行
                                        wrappedLines.Add(newLine);
                                        newLine = word + " ";
                                        currentWidth = wordWidth;
                                    }
                                    else
                                    {
                                        newLine += word + " ";
                                        currentWidth += wordWidth;
                                    }
                                }
                                wrappedLines.Add(newLine);

                                // 繪製每一行
                                foreach (string wrappedLine in wrappedLines)
                                {
                                    RectangleF textRect = new RectangleF(10, currentY, maxWidth, lineHeight);
                                    gr.DrawString(wrappedLine, font, Brushes.Gray, textRect, stringFormat);
                                    currentY += lineHeight;
                                }


                            }
                        }
                    }

                // 在畫面中央顯示狀態提示
                using (Font font = new Font("微軟正黑體", 16))
                using (StringFormat stringFormat = new StringFormat()
                {
                    Alignment = StringAlignment.Center,
                    LineAlignment = StringAlignment.Center
                })
                {
                    RectangleF statusRect = new RectangleF(
                        mainWindow.ClientRectangle.Width / 2f - 150,
                        mainWindow.ClientRectangle.Height / 2f - 50,
                        300,
                        100
                    );

                    // 顯示洗牌提示
                    if (isShuffling)
                    {
                        gr.DrawString("正在洗牌...", font, Brushes.Gray, statusRect, stringFormat);
                    }
                    // API 讀取中提示
                    else if (showTarot && string.IsNullOrEmpty(apiResponse))
                    {
                        gr.DrawString("正在解讀卡牌...", font, Brushes.Gray, statusRect, stringFormat);
                    }
                }

                if (true)
                {
                    float rightEdge = mainWindow.ClientRectangle.Right - 150;

                    using (Font priceFont = new Font("Arial", 10))
                    using (StringFormat rightAlign = new StringFormat { Alignment = StringAlignment.Far })
                    {
                        // 使用實際的 Level 值來顯示標籤
                        if (HighestLine.Level != 0)
                        {
                            // 顯示 TP 線
                            DrawPriceLabel("HighNormal", FormatSymbolPriceUp(HighestLine.Level), rightEdge, 0,
                                priceFont, rightAlign, gr, Color.Yellow, true, FormatSymbolPriceUp(HigherFibo2.Level));
                            // 顯示 1.382 線
                            DrawPriceLabel("SellLineToLowNormal", FormatSymbolPriceDown(HigherFibo1.Level), rightEdge, 0,
                                priceFont, rightAlign, gr, Color.Yellow, true, FormatSymbolPriceUp(LowestLine.Level));
                            // 顯示 1.618 線
                            DrawPriceLabel("SellLine", FormatSymbolPriceDown(HigherFibo2.Level), rightEdge, 0,
                                priceFont, rightAlign, gr, Color.Yellow, true, FormatSymbolPriceUp(LowerFibo2.Level));
                            

                            // 顯示止損線，並傳入基準價格
                            DrawPriceLabel("SellStop0", FormatSymbolPriceUp(HigherFibo1StopLossLine.Level), rightEdge, 0,
                                priceFont, rightAlign, gr, Color.Red, true, FormatSymbolPriceDown(HigherFibo1.Level));
                            DrawPriceLabel("SellStop", FormatSymbolPriceUp(HigherFibo2StopLossLine.Level), rightEdge, 0,
                                priceFont, rightAlign, gr, Color.Red, true, FormatSymbolPriceDown(HigherFibo2.Level));
                        }

                        if (LowestLine.Level != 0)
                        {
                            // 顯示 TP 線
                            DrawPriceLabel("LowNormal", FormatSymbolPriceDown(LowestLine.Level), rightEdge, 0,
                                priceFont, rightAlign, gr, Color.Yellow, true, FormatSymbolPriceDown(LowerFibo2.Level));
                            // 顯示 1.382 線
                            DrawPriceLabel("BuyLineToHighNormal", FormatSymbolPriceUp(LowerFibo1.Level), rightEdge, 0,
                                priceFont, rightAlign, gr, Color.Yellow, true, FormatSymbolPriceDown(HighestLine.Level));
                            // 顯示 1.618 線
                            DrawPriceLabel("BuyLine", FormatSymbolPriceUp(LowerFibo2.Level), rightEdge, 0,
                                priceFont, rightAlign, gr, Color.Yellow, true, FormatSymbolPriceDown(HigherFibo2.Level));
                                
                            // 顯示止損線，並傳入基準價格
                            DrawPriceLabel("BuyStop0", FormatSymbolPriceDown(LowerFibo1StopLossLine.Level), rightEdge, 0,
                                priceFont, rightAlign, gr, Color.Red, true, FormatSymbolPriceUp(LowerFibo1.Level));
                            DrawPriceLabel("BuyStop", FormatSymbolPriceDown(LowerFibo2StopLossLine.Level), rightEdge, 0,
                                priceFont, rightAlign, gr, Color.Red, true, FormatSymbolPriceUp(LowerFibo2.Level));
                        }
                    }
                }

                // 繪製賣出和買入按鈕
                using (var buttonBrush = new SolidBrush(Color.FromArgb(80, 45, 45, 45)))
                using (var hoverBrush = new SolidBrush(Color.FromArgb(180, 64, 64, 64)))
                using (var borderPen = new Pen(Color.FromArgb(120, 100, 100, 100)))
                using (var textBrush = new SolidBrush(Color.FromArgb(220, 255, 255, 255)))
                using (var font = new Font("Arial", 9))
                {
                    var format = new StringFormat
                    {
                        Alignment = StringAlignment.Center,
                        LineAlignment = StringAlignment.Center
                    };

                    // 如果需要顯示按鈕
                    if (showButtons)
                    {
                        // 左邊按鈕（下單）- 在鼠標左側，Y位置與鼠標一致
                        Rectangle leftButtonRect = new Rectangle(buttonPosition.X - 165, buttonPosition.Y - 12, 80, 25);
                        // 中間按鈕（API）- 在鼠標中間，Y位置與鼠標一致
                        Rectangle middleButtonRect = new Rectangle(buttonPosition.X - 80, buttonPosition.Y - 12, 80, 25);
                        // 右邊按鈕（APIX）- 在鼠標右側，Y位置與鼠標一致
                        Rectangle rightButtonRect = new Rectangle(buttonPosition.X + 5, buttonPosition.Y - 12, 80, 25);
                        
                        bool isLeftButtonHovered = leftButtonRect.Contains(currentMousePosition);
                        bool isMiddleButtonHovered = middleButtonRect.Contains(currentMousePosition);
                        bool isRightButtonHovered = rightButtonRect.Contains(currentMousePosition);

                        // 根據按鈕類型選擇顏色
                        Color buttonColor;
                        Color hoverColor;
                        string buttonBaseText = "";
                        
                        switch (activeButton)
                        {
                            case HoveredButton.SellButton2618:
                                buttonColor = sellButton2618Color;
                                hoverColor = orangeButtonHoverColor;
                                buttonBaseText = "賣";
                                break;
                            case HoveredButton.SellButton1618:
                                buttonColor = sellButton1618Color;
                                hoverColor = sellButtonHoverColor;
                                buttonBaseText = "賣";
                                break;
                            case HoveredButton.SellButton1382:
                                buttonColor = sellButton1382Color;
                                hoverColor = sellButtonHoverColor;
                                buttonBaseText = "賣";
                                break;
                            case HoveredButton.BuyButton2618:
                                buttonColor = buyButton2618Color;
                                hoverColor = orangeButtonHoverColor;
                                buttonBaseText = "買";
                                break;
                            case HoveredButton.BuyButton1618:
                                buttonColor = buyButton1618Color;
                                hoverColor = buyButtonHoverColor;
                                buttonBaseText = "買";
                                break;
                            case HoveredButton.BuyButton1382:
                                buttonColor = buyButton1382Color;
                                hoverColor = buyButtonHoverColor;
                                buttonBaseText = "買";
                                break;
                            default:
                                buttonColor = buttonBrush.Color;
                                hoverColor = buttonBrush.Color;
                                buttonBaseText = "";
                                break;
                        }

                        using (var customButtonBrush = new SolidBrush(buttonColor))
                        using (var hoverButtonBrush = new SolidBrush(hoverColor))
                        {
                            // 繪製左邊按鈕（下單）
                            gr.FillRectangle(isLeftButtonHovered ? hoverButtonBrush : customButtonBrush, leftButtonRect);
                            gr.DrawRectangle(borderPen, leftButtonRect);
                            gr.DrawString(GetButtonText(activeButton, buttonBaseText), font, textBrush, leftButtonRect, format);

                            // 繪製中間按鈕（API）
                            gr.FillRectangle(isMiddleButtonHovered ? hoverButtonBrush : customButtonBrush, middleButtonRect);
                            gr.DrawRectangle(borderPen, middleButtonRect);
                            gr.DrawString(GetButtonText(activeButton, buttonBaseText + "API"), font, textBrush, middleButtonRect, format);

                            // 繪製右邊按鈕（APIX）
                            gr.FillRectangle(isRightButtonHovered ? hoverButtonBrush : customButtonBrush, rightButtonRect);
                            gr.DrawRectangle(borderPen, rightButtonRect);
                            gr.DrawString(GetButtonText(activeButton, buttonBaseText + "X"), font, textBrush, rightButtonRect, format);
                        }

                        // 如果任一按鈕被懸停，顯示提示信息
                        if (isLeftButtonHovered || isMiddleButtonHovered || isRightButtonHovered)
                        {
                            string tooltip = "";
                            switch (activeButton)
                            {
                                case HoveredButton.SellButton2618:
                                    string toTP1 = FormatPriceDifference(HigherFibo3.Level, 
                                        isLongMode ? LowestLine.Level : HigherFibo2.Level, false);
                                    string toSL1 = FormatPriceDifference(HigherFibo3.Level, HigherFibo3StopLossLine.Level, false);
                                    
                                    double checkATRpercent1 = GetPercentATR() / Math.Abs(HigherFibo1.Level - HigherFibo2.Level) * 100;
                                    bool isSuitable1 = checkATRpercent1 > 61.8;
                                    tooltip = $"賣出2.618\nTP: {toTP1}\nSL: {toSL1}\n模式: {(isLongMode ? "長線" : "短線")}{(!isSuitable1 ? "\n⚠️ 此週期ATR過小不適合操作" : "")}";
                                    break;
                                case HoveredButton.SellButton1618:
                                    string toTP2 = FormatPriceDifference(HigherFibo2.Level, 
                                        isLongMode ? LowestLine.Level : HighestLine.Level, false);
                                    string toSL2 = FormatPriceDifference(HigherFibo2.Level, HigherFibo2StopLossLine.Level, false);
                                    
                                    double checkATRpercent2 = GetPercentATR() / Math.Abs(HigherFibo1.Level - HigherFibo2.Level) * 100;
                                    bool isSuitable2 = checkATRpercent2 > 61.8;
                                    tooltip = $"賣出1.618\nTP: {toTP2}\nSL: {toSL2}\n模式: {(isLongMode ? "長線" : "短線")}{(!isSuitable2 ? "\n⚠️ 此週期ATR過小不適合操作" : "")}";
                                    break;
                                case HoveredButton.SellButton1382:
                                    string toTP3 = FormatPriceDifference(HigherFibo1.Level, 
                                        isLongMode ? LowestLine.Level : LowerFiboInside.Level, false);
                                    string toSL3 = FormatPriceDifference(HigherFibo1.Level, HigherFibo1StopLossLine.Level, false);
                                    
                                    double checkATRpercent3 = GetPercentATR() / Math.Abs(HigherFibo1.Level - HigherFibo2.Level) * 100;
                                    bool isSuitable3 = checkATRpercent3 > 61.8;
                                    tooltip = $"賣出1.382\nTP: {toTP3}\nSL: {toSL3}\n模式: {(isLongMode ? "長線" : "短線")}{(!isSuitable3 ? "\n⚠️ 此週期ATR過小不適合操作" : "")}";
                                    break;
                                case HoveredButton.BuyButton2618:
                                    string toTP4 = FormatPriceDifference(LowerFibo3.Level, 
                                        isLongMode ? HighestLine.Level : LowerFibo2.Level, true);
                                    string toSL4 = FormatPriceDifference(LowerFibo3.Level, LowerFibo3StopLossLine.Level, true);
                                    
                                    double checkATRpercent4 = GetPercentATR() / Math.Abs(LowerFibo1.Level - LowerFibo2.Level) * 100;
                                    bool isSuitable4 = checkATRpercent4 > 61.8;
                                    tooltip = $"買入2.618\nTP: {toTP4}\nSL: {toSL4}\n模式: {(isLongMode ? "長線" : "短線")}{(!isSuitable4 ? "\n⚠️ 此週期ATR過小不適合操作" : "")}";
                                    break;
                                case HoveredButton.BuyButton1618:
                                    string toTP5 = FormatPriceDifference(LowerFibo2.Level, 
                                        isLongMode ? HighestLine.Level : LowestLine.Level, true);
                                    string toSL5 = FormatPriceDifference(LowerFibo2.Level, LowerFibo2StopLossLine.Level, true);
                                    
                                    double checkATRpercent5 = GetPercentATR() / Math.Abs(LowerFibo1.Level - LowerFibo2.Level) * 100;
                                    bool isSuitable5 = checkATRpercent5 > 61.8;
                                    tooltip = $"買入1.618\nTP: {toTP5}\nSL: {toSL5}\n模式: {(isLongMode ? "長線" : "短線")}{(!isSuitable5 ? "\n⚠️ 此週期ATR過小不適合操作" : "")}";
                                    break;
                                case HoveredButton.BuyButton1382:
                                    string toTP6 = FormatPriceDifference(LowerFibo1.Level, 
                                        isLongMode ? HighestLine.Level : HigherFiboInside.Level, true);
                                    string toSL6 = FormatPriceDifference(LowerFibo1.Level, LowerFibo1StopLossLine.Level, true);
                                    
                                    double checkATRpercent6 = GetPercentATR() / Math.Abs(LowerFibo1.Level - LowerFibo2.Level) * 100;
                                    bool isSuitable6 = checkATRpercent6 > 61.8;
                                    tooltip = $"買入1.382\nTP: {toTP6}\nSL: {toSL6}\n模式: {(isLongMode ? "長線" : "短線")}{(!isSuitable6 ? "\n⚠️ 此週期ATR過小不適合操作" : "")}";
                                    break;
                            }
                            if (!string.IsNullOrEmpty(tooltip))
                            {
                                DrawTooltip(gr, tooltip, currentMousePosition, font);
                            }
                        }
                    }
                }

                // 繪製FiboType選擇輪
                if (showFiboWheel)
                {
                    DrawFiboWheel(gr);
                }
            }
            finally
            {
                gr.SetClip(prevClip);
            }
        }

        private double GetTickCost_local()
        {

            double b = (Symbol.GetTickCost(Symbol.SymbolType==SymbolType.Futures?Symbol.Last:Symbol.Bid));
            return b  ;
        }
        // 添加繪製價格標籤的輔助方法
        private void DrawPriceLabel(string label, double price, float x, float y, Font font, StringFormat format,
            Graphics gr, Color color, bool isStop = false, double basePrice = 0)
        {
            float priceY = (float)this.CurrentChart.MainWindow.CoordinatesConverter.GetChartY(price);

            string priceText;
            if (isStop)
            {
                // 計算止損金額和Tick數
                int tickCount = CalcTicks(price, basePrice);
                double lossAmount = (Math.Abs(price - basePrice) / Symbol.TickSize) * GetTickCost_local();

                // 簡化顯示格式，確保文本更簡潔
                priceText = $"{label} {Symbol.FormatPrice(price)} ({tickCount}t ${lossAmount:F2})";
            }
            else
            {
                priceText = $"{label} {Symbol.FormatPrice(price)}";
            }

            // 增加文本框寬度，並向左移動起始位置
            float rightEdge = this.CurrentChart.MainWindow.ClientRectangle.Right - 10;
            float textWidth = isStop ? 300 : 200; // Stop 文本使用更寬的寬度

            // 向左移動文本框起始位置
            RectangleF rect = new RectangleF(rightEdge - textWidth, priceY - 10, textWidth, 20);

            using (SolidBrush brush = new SolidBrush(color))
            {
                gr.DrawString(priceText, font, brush, rect, format);
            }
        }

        // 修改價格差異計算方法，加入交易方向參數
        private string FormatPriceDifference(double entryPrice, double targetPrice, bool isBuy = true)
        {
            // 根據交易方向和目標類型（SL/TP）決定如何格式化價格
            double formattedEntryPrice = isBuy ? FormatSymbolPriceUp(entryPrice) : FormatSymbolPriceDown(entryPrice);
            double formattedTargetPrice;

            // 如果是做多
            if (isBuy)
            {
                // TP向上取整，SL向下取整
                formattedTargetPrice = targetPrice > entryPrice ? 
                    FormatSymbolPriceUp(targetPrice) :    // TP
                    FormatSymbolPriceDown(targetPrice);   // SL
            }
            // 如果是做空
            else
            {
                // TP向下取整，SL向上取整
                formattedTargetPrice = targetPrice < entryPrice ? 
                    FormatSymbolPriceDown(targetPrice) :  // TP
                    FormatSymbolPriceUp(targetPrice);     // SL
            }

            // 計算tick數和美金價值
            int ticks = (int)Math.Abs(Symbol.CalculateTicks(formattedEntryPrice, formattedTargetPrice));
            double tickCost = GetTickCost_local();
            double dollarValue = ticks * tickCost;

            return $"{ticks}t ${dollarValue:F2} ";
        }

        // 修改懸浮提示中的計算方法調用
        private void DrawTooltip(Graphics gr, string text, Point position, Font font)
        {
            // 測量文字大小
            var lines = text.Split('\n');
            float maxWidth = 0;
            float totalHeight = 0;
            foreach (var line in lines)
            {
                var size = gr.MeasureString(line, font);
                maxWidth = Math.Max(maxWidth, size.Width);
                totalHeight += size.Height;
            }

            // 計算提示框位置
            float x = position.X + 10;
            float y = position.Y - totalHeight - 10;

            // 確保提示框在視窗內
            if (x + maxWidth > this.CurrentChart.MainWindow.ClientRectangle.Right)
                x = position.X - maxWidth - 10;
            if (y < 0)
                y = position.Y + 20;

            // 繪製背景
            using (var bgBrush = new SolidBrush(Color.FromArgb(200, 0, 0, 0)))
            {
                gr.FillRectangle(bgBrush, x - 5, y - 5, maxWidth + 10, totalHeight + 10);
            }

            // 繪製文字
            using (var textBrush = new SolidBrush(Color.White))
            {
                float currentY = y;
                foreach (var line in lines)
                {
                    if (line.StartsWith("TP:") || line.StartsWith("SL:"))
                    {
                        // 解析金額
                        string[] parts = line.Split('$');
                        if (parts.Length == 2)
                        {
                            double amount = double.Parse(parts[1].Trim());
                            double totalAmount = amount * this.Quantity * ((Symbol.SymbolType==SymbolType.Futures)?1.0:Symbol.TickSize);
                            
                            if (line.StartsWith("TP:"))
                            {
                                if (totalAmount >= EvalProfitTarget)
                                {
                                    // 使用黃色顯示並添加"（通過）"字樣
                                    using (var yellowBrush = new SolidBrush(Color.Yellow))
                                    {
                                        gr.DrawString($"{parts[0]}$ {totalAmount:F2}", font, yellowBrush, x, currentY);
                                    }
                                }
                                else
                                {
                                    // 計算所需的Quantity
                                    double requiredQuantity = Math.Ceiling(EvalProfitTarget / amount);
                                    gr.DrawString($"{parts[0]}$ {totalAmount:F2}", font, textBrush, x, currentY);
                                    currentY += gr.MeasureString(line, font).Height;
                                    // 顯示所需的Quantity
                                    using (var redBrush = new SolidBrush(Color.Red))
                                    {
                                        gr.DrawString($"需要Quantity: {requiredQuantity:F2}", font, redBrush, x, currentY);
                                    }
                                }
                            }
                            else
                            {
                                gr.DrawString($"{parts[0]}$ {totalAmount:F2}", font, textBrush, x, currentY);
                            }
                        }
                    }
                    else
                    {
                        gr.DrawString(line, font, textBrush, x, currentY);
                    }
                    currentY += gr.MeasureString(line, font).Height;
                }
            }
        }

        public override void Dispose()
        {
            if (this.CurrentChart != null)
            {
                this.CurrentChart.MouseDown -= CurrentChart_MouseDown;
                this.CurrentChart.MouseUp -= CurrentChart_MouseUp;
                this.CurrentChart.MouseMove -= CurrentChart_MouseMove;
                this.CurrentChart.MouseLeave -= CurrentChart_MouseLeave;
            }

            // 清理Volume Analysis相關資源
            if (volumeAnalysisProgress != null && volumeAnalysisProgress.State != VolumeAnalysisCalculationState.Finished)
            {
                volumeAnalysisProgress.AbortLoading();
            }

            httpClient.Dispose(); // Dispose of the HttpClient instance
            cancellationTokenSource.Dispose();//dispose of the cancellation token
            random?.Dispose(); // Dispose of the crypto random generator

            // 清理塔羅牌圖片資源
            if (tarotImages != null)
            {
                foreach (var image in tarotImages.Values)
                {
                    image?.Dispose();
                }
                tarotImages.Clear();
            }
            cardBackImage?.Dispose();

            base.Dispose();
        }

        protected override void OnUpdate(UpdateArgs args) { }

        // Helper class to store both English and Chinese names for Tarot cards

        public double FormatSymbolPrice(double price, bool roundUp = true)
        {
            Symbol symbol = this.Symbol;
            if (symbol == null)
                throw new ArgumentNullException(nameof(symbol));

            double tickSize = symbol.TickSize;

            // 計算需要進位的小數位數
            int decimalPlaces = BitConverter.GetBytes(decimal.GetBits((decimal)tickSize)[3])[2];

            // 計算價格除以 tickSize 的商和餘數
            double quotient = Math.Floor(price / tickSize);
            double remainder = price % tickSize;

            double roundedPrice;
            if (roundUp)
            {
                // 向上取整：如果有餘數，就進位到下一個 tick
                roundedPrice = remainder > 0
                    ? (quotient + 1) * tickSize
                    : quotient * tickSize;
            }
            else
            {
                // 向下取整：直接捨去餘數
                roundedPrice = quotient * tickSize;
            }

            // 格式化價格
            if (double.TryParse(symbol.FormatPrice(roundedPrice), out double formattedPrice))
            {
                return formattedPrice;
            }

            return roundedPrice;
        }

        // 為了方便使用，可以添加兩個輔助方法
        public double FormatSymbolPriceUp(double price)
        {
            return FormatSymbolPrice(price, true);
        }

        public double FormatSymbolPriceDown(double price)
        {
            return FormatSymbolPrice(price, false);
        }

        // 添加這兩個輔助方法到類中
        private int CalcTicks(double price1, double price2)
        {
            // 計算兩個價格之間的 Tick 數
            double priceDiff = Math.Abs(price1 - price2);
            return (int)Math.Round(priceDiff / Symbol.TickSize);
        }

        private double CalcPrice(double price, int ticks)
        {
            // 根據基準價格和 Tick 數計算目標價格
            return price + (ticks * Symbol.TickSize);
        }

        

        

        private void CheckAndShowButtons(Point location)
        {
            // 如果總開關未啟用，不顯示任何按鈕
            if (!isMasterButtonEnabled)
            {
                showButtons = false;
                activeButton = HoveredButton.None;
                return;
            }

            // 計算主視窗的有效檢測範圍（中間80%）
            var mainWindow = this.CurrentChart.MainWindow;
            int windowWidth = mainWindow.ClientRectangle.Width;
            int leftBoundary = (int)(windowWidth * 0.1); // 左邊10%
            int rightBoundary = (int)(windowWidth * 0.9); // 右邊10%

            // 檢查鼠標是否在有效範圍內
            if (location.X < leftBoundary || location.X > rightBoundary)
            {
                showButtons = false;
                activeButton = HoveredButton.None;
                this.CurrentChart.RedrawBuffer();
                return;
            }

            Dictionary<HoveredButton, float> buttonDistances = new Dictionary<HoveredButton, float>();
            Dictionary<HoveredButton, float> buttonYPositions = new Dictionary<HoveredButton, float>();

            // 收集所有價格線的距離和Y坐標
            if (HigherFibo3.Level != 0)
            {
                float sellY = (float)this.CurrentChart.MainWindow.CoordinatesConverter.GetChartY(HigherFibo3.Level);
                buttonDistances[HoveredButton.SellButton2618] = Math.Abs(location.Y - sellY);
                buttonYPositions[HoveredButton.SellButton2618] = sellY;
            }

            if (HigherFibo2.Level != 0)
            {
                float sellY = (float)this.CurrentChart.MainWindow.CoordinatesConverter.GetChartY(HigherFibo2.Level);
                buttonDistances[HoveredButton.SellButton1618] = Math.Abs(location.Y - sellY);
                buttonYPositions[HoveredButton.SellButton1618] = sellY;
            }

            if (HigherFibo1.Level != 0)
            {
                float sellY = (float)this.CurrentChart.MainWindow.CoordinatesConverter.GetChartY(HigherFibo1.Level);
                buttonDistances[HoveredButton.SellButton1382] = Math.Abs(location.Y - sellY);
                buttonYPositions[HoveredButton.SellButton1382] = sellY;
            }

            if (LowerFibo3.Level != 0)
            {
                float buyY = (float)this.CurrentChart.MainWindow.CoordinatesConverter.GetChartY(LowerFibo3.Level);
                buttonDistances[HoveredButton.BuyButton2618] = Math.Abs(location.Y - buyY);
                buttonYPositions[HoveredButton.BuyButton2618] = buyY;
            }

            if (LowerFibo2.Level != 0)
            {
                float buyY = (float)this.CurrentChart.MainWindow.CoordinatesConverter.GetChartY(LowerFibo2.Level);
                buttonDistances[HoveredButton.BuyButton1618] = Math.Abs(location.Y - buyY);
                buttonYPositions[HoveredButton.BuyButton1618] = buyY;
            }

            if (LowerFibo1.Level != 0)
            {
                float buyY = (float)this.CurrentChart.MainWindow.CoordinatesConverter.GetChartY(LowerFibo1.Level);
                buttonDistances[HoveredButton.BuyButton1382] = Math.Abs(location.Y - buyY);
                buttonYPositions[HoveredButton.BuyButton1382] = buyY;
            }

            // 找出最近的價格線
            if (buttonDistances.Count > 0)
            {
                var nearestButton = buttonDistances.OrderBy(x => x.Value).First();
                if (nearestButton.Value <= PRICE_HOVER_THRESHOLD)
                {
                    // 只有在按鈕未顯示或者切換到不同的按鈕時才更新位置
                    if (!showButtons || activeButton != nearestButton.Key)
                    {
                        // 使用價格線的Y坐標，但保持鼠標的X坐標
                        buttonPosition = new Point(location.X, (int)buttonYPositions[nearestButton.Key]);
                        activeButton = nearestButton.Key;
                    }
                    showButtons = true;
                    this.CurrentChart.RedrawBuffer();
                    return;
                }
            }

            // 如果沒有找到足夠近的價格線，隱藏按鈕
            showButtons = false;
            activeButton = HoveredButton.None;
            this.CurrentChart.RedrawBuffer();
        }

        private void HandleOrderButton()
        {
            var thisFn = SendOrderStrategy;
            switch (activeButton)
            {
                case HoveredButton.SellButton2618:
                    thisFn("Sell", FormatSymbolPriceDown(HigherFibo3.Level), FormatSymbolPriceUp(HigherFibo3StopLossLine.Level), 
                        !is11Mode ? (isLongMode ? FormatSymbolPriceUp(LowestLine.Level) : FormatSymbolPriceUp(HigherFibo2.Level)): FormatSymbolPriceUp(HigherFibo311Line.Level), activeButton);
                    break;
                case HoveredButton.SellButton1618:
                    thisFn("Sell", FormatSymbolPriceDown(HigherFibo2.Level), FormatSymbolPriceUp(HigherFibo2StopLossLine.Level),
                        !is11Mode ? (isLongMode ? FormatSymbolPriceUp(LowestLine.Level) : FormatSymbolPriceUp(HighestLine.Level)) : FormatSymbolPriceUp(HigherFibo211Line.Level), activeButton);
                    break;
                case HoveredButton.SellButton1382:
                    thisFn("Sell", FormatSymbolPriceDown(HigherFibo1.Level), FormatSymbolPriceUp(HigherFibo1StopLossLine.Level),
                        !is11Mode ? (isLongMode ? FormatSymbolPriceUp(LowestLine.Level) : FormatSymbolPriceUp(LowerFiboInside.Level)) : FormatSymbolPriceUp(HigherFibo111Line.Level), activeButton);
                    break;
                case HoveredButton.BuyButton2618:
                    thisFn("Buy", FormatSymbolPriceUp(LowerFibo3.Level), FormatSymbolPriceDown(LowerFibo3StopLossLine.Level),
                        !is11Mode ? (isLongMode ? FormatSymbolPriceDown(HighestLine.Level) : FormatSymbolPriceDown(LowerFibo2.Level) ) : FormatSymbolPriceDown(LowerFibo311Line.Level), activeButton);
                    break;
                case HoveredButton.BuyButton1618:
                    thisFn("Buy", FormatSymbolPriceUp(LowerFibo2.Level), FormatSymbolPriceDown(LowerFibo2StopLossLine.Level),
                        !is11Mode ? (isLongMode ? FormatSymbolPriceDown(HighestLine.Level) : FormatSymbolPriceDown(LowestLine.Level) ) : FormatSymbolPriceDown(LowerFibo211Line.Level), activeButton);
                    break;
                case HoveredButton.BuyButton1382:
                    thisFn("Buy", FormatSymbolPriceUp(LowerFibo1.Level), FormatSymbolPriceDown(LowerFibo1StopLossLine.Level),
                        !is11Mode ? (isLongMode ? FormatSymbolPriceDown(HighestLine.Level) : FormatSymbolPriceDown(HigherFiboInside.Level) ) : FormatSymbolPriceDown(LowerFibo111Line.Level), activeButton);
                    break;
            }
            showButtons = false;
            activeButton = HoveredButton.None;
            this.CurrentChart.RedrawBuffer();
        }

        private void HandleApiButton()
        {
            
            var thisFn = SendOrderToApi;
            switch (activeButton)
            {
                case HoveredButton.SellButton2618:
                    thisFn("Sell", FormatSymbolPriceDown(HigherFibo3.Level), FormatSymbolPriceUp(HigherFibo3StopLossLine.Level), 
                        !is11Mode ? (isLongMode ? FormatSymbolPriceUp(LowestLine.Level) : FormatSymbolPriceUp(HigherFibo2.Level)): FormatSymbolPriceUp(HigherFibo311Line.Level), activeButton);
                    break;
                case HoveredButton.SellButton1618:
                    thisFn("Sell", FormatSymbolPriceDown(HigherFibo2.Level), FormatSymbolPriceUp(HigherFibo2StopLossLine.Level),
                        !is11Mode ? (isLongMode ? FormatSymbolPriceUp(LowestLine.Level) : FormatSymbolPriceUp(HighestLine.Level)) : FormatSymbolPriceUp(HigherFibo211Line.Level), activeButton);
                    break;
                case HoveredButton.SellButton1382:
                    thisFn("Sell", FormatSymbolPriceDown(HigherFibo1.Level), FormatSymbolPriceUp(HigherFibo1StopLossLine.Level),
                        !is11Mode ? (isLongMode ? FormatSymbolPriceUp(LowestLine.Level) : FormatSymbolPriceUp(LowerFiboInside.Level)) : FormatSymbolPriceUp(HigherFibo111Line.Level), activeButton);
                    break;
                case HoveredButton.BuyButton2618:
                    thisFn("Buy", FormatSymbolPriceUp(LowerFibo3.Level), FormatSymbolPriceDown(LowerFibo3StopLossLine.Level),
                        !is11Mode ? (isLongMode ? FormatSymbolPriceDown(HighestLine.Level) : FormatSymbolPriceDown(LowerFibo2.Level) ) : FormatSymbolPriceDown(LowerFibo311Line.Level), activeButton);
                    break;
                case HoveredButton.BuyButton1618:
                    thisFn("Buy", FormatSymbolPriceUp(LowerFibo2.Level), FormatSymbolPriceDown(LowerFibo2StopLossLine.Level),
                        !is11Mode ? (isLongMode ? FormatSymbolPriceDown(HighestLine.Level) : FormatSymbolPriceDown(LowestLine.Level) ) : FormatSymbolPriceDown(LowerFibo211Line.Level), activeButton);
                    break;
                case HoveredButton.BuyButton1382:
                    thisFn("Buy", FormatSymbolPriceUp(LowerFibo1.Level), FormatSymbolPriceDown(LowerFibo1StopLossLine.Level),
                        !is11Mode ? (isLongMode ? FormatSymbolPriceDown(HighestLine.Level) : FormatSymbolPriceDown(HigherFiboInside.Level) ) : FormatSymbolPriceDown(LowerFibo111Line.Level), activeButton);
                    break;
            }
            showButtons = false;
            activeButton = HoveredButton.None;
            this.CurrentChart.RedrawBuffer();
        }

        private void HandleApixButton()
        {
            
            var thisFn = SendOrderToApix;
            switch (activeButton)
            {
                case HoveredButton.SellButton2618:
                    thisFn("Sell", FormatSymbolPriceDown(HigherFibo3.Level), FormatSymbolPriceUp(HigherFibo3StopLossLine.Level), 
                        !is11Mode ? (isLongMode ? FormatSymbolPriceUp(LowestLine.Level) : FormatSymbolPriceUp(HigherFibo2.Level)): FormatSymbolPriceUp(HigherFibo311Line.Level), activeButton);
                    break;
                case HoveredButton.SellButton1618:
                    thisFn("Sell", FormatSymbolPriceDown(HigherFibo2.Level), FormatSymbolPriceUp(HigherFibo2StopLossLine.Level),
                        !is11Mode ? (isLongMode ? FormatSymbolPriceUp(LowestLine.Level) : FormatSymbolPriceUp(HighestLine.Level)) : FormatSymbolPriceUp(HigherFibo211Line.Level), activeButton);
                    break;
                case HoveredButton.SellButton1382:
                    thisFn("Sell", FormatSymbolPriceDown(HigherFibo1.Level), FormatSymbolPriceUp(HigherFibo1StopLossLine.Level),
                        !is11Mode ? (isLongMode ? FormatSymbolPriceUp(LowestLine.Level) : FormatSymbolPriceUp(LowerFiboInside.Level)) : FormatSymbolPriceUp(HigherFibo111Line.Level), activeButton);
                    break;
                case HoveredButton.BuyButton2618:
                    thisFn("Buy", FormatSymbolPriceUp(LowerFibo3.Level), FormatSymbolPriceDown(LowerFibo3StopLossLine.Level),
                        !is11Mode ? (isLongMode ? FormatSymbolPriceDown(HighestLine.Level) : FormatSymbolPriceDown(LowerFibo2.Level) ) : FormatSymbolPriceDown(LowerFibo311Line.Level), activeButton);
                    break;
                case HoveredButton.BuyButton1618:
                    thisFn("Buy", FormatSymbolPriceUp(LowerFibo2.Level), FormatSymbolPriceDown(LowerFibo2StopLossLine.Level),
                        !is11Mode ? (isLongMode ? FormatSymbolPriceDown(HighestLine.Level) : FormatSymbolPriceDown(LowestLine.Level) ) : FormatSymbolPriceDown(LowerFibo211Line.Level), activeButton);
                    break;
                case HoveredButton.BuyButton1382:
                    thisFn("Buy", FormatSymbolPriceUp(LowerFibo1.Level), FormatSymbolPriceDown(LowerFibo1StopLossLine.Level),
                        !is11Mode ? (isLongMode ? FormatSymbolPriceDown(HighestLine.Level) : FormatSymbolPriceDown(HigherFiboInside.Level) ) : FormatSymbolPriceDown(LowerFibo111Line.Level), activeButton);
                    break;
            }
            showButtons = false;
            activeButton = HoveredButton.None;
            this.CurrentChart.RedrawBuffer();
        }

        /// <summary>
    /// 創建訂單策略，包括入場訂單和止損止盈
    /// </summary>
    /// <param name="side">交易方向（買/賣）</param>
    /// <param name="price">入場價格</param>
    /// <param name="orderType">訂單類型（市價/限價/停損）</param>
    private void SendOrderStrategy(string side, double price, double sl, double tp, HoveredButton button)
    {
        try
        {
                string orderType = "Limit";

            double entryPrice = price;  
            double slPrice = sl;
            double tpPrice = tp;

            // 根據買賣方向和訂單類型處理價格
            double formattedPrice = side == "Buy" ? 
                FormatSymbolPriceUp(entryPrice) : 
                FormatSymbolPriceDown(entryPrice);

            
            // 計算止損和止盈的Ticks
            double slTicks = Math.Abs(this.Symbol.CalculateTicks(formattedPrice, slPrice));
            double tpTicks = Math.Abs(this.Symbol.CalculateTicks(formattedPrice, tpPrice));

            // 創建下單請求
            var request = new PlaceOrderRequestParameters()
            {
                Account = this.CurrentChart.Account,
                Symbol = this.Symbol,
                TimeInForce = TimeInForce.GTC,
                Quantity = this.Quantity,
                Side = side == "Buy" ? Side.Buy : Side.Sell,
                StopLoss = SlTpHolder.CreateSL(slTicks, PriceMeasurement.Offset),
                TakeProfit = SlTpHolder.CreateTP(tpTicks, PriceMeasurement.Offset)
            };

            // 根據訂單類型設置價格
            string selectedOrderType = Core.OrderTypes.FirstOrDefault(x => 
                x.ConnectionId == this.Symbol.ConnectionId && 
                x.Behavior.ToString().ToUpper() == orderType.ToUpper())?.Id;

            if (string.IsNullOrEmpty(selectedOrderType))
            {
                //Core.Instance.Loggers.Log($"Connection does not support {orderType} orders");
                return;
            }

            request.OrderTypeId = selectedOrderType;
            if (orderType.ToUpper() == "STOP")
            {
                request.TriggerPrice = formattedPrice;
            }
            else
            {
                request.Price = formattedPrice;
            }

            // 發送訂單
            var result = Core.Instance.PlaceOrder(request);

            if (result.Status == TradingOperationResultStatus.Failure)
            {
                //Core.Instance.Loggers.Log($"Place {side} {orderType} order refused: {result.Message}");
            }
            else
            {
                //Core.Instance.Loggers.Log($"{side} {orderType} order placed at {formattedPrice}");
            }
        }
        catch (Exception ex)
        {
            //Core.Instance.Loggers.Log($"Error placing order: {ex.Message}");
            }
        }

        // 添加發送訂單到API的方法
        private async Task SendOrderToApi(string direction, double price, double slPrice, double tpPrice, HoveredButton button)
        {
            try
            {
                var symbol = this.Symbol;
                var symbolId = symbol.AdditionalInfo != null && symbol.AdditionalInfo.TryGetItem(Symbol.TRADING_SYMBOL_ID, out var item) ? item.Value.ToString() : symbol.Id;
                int atIndex = symbolId.IndexOf('@');
                string tradableSymbolId = atIndex > 0 ? symbolId.Substring(0, atIndex) : symbolId;

                var orderData = new
                {
                    quantity = this.Quantity,
                    price = price,
                    tp_price = tpPrice,
                    sl_price = slPrice,
                    symbol = tradableSymbolId,
                    direction = direction
                };

                using (var httpClient = new HttpClient())
                {
                    var response = await httpClient.PostAsync(
                        ORDER_API_ENDPOINT,
                        new StringContent(JsonSerializer.Serialize(orderData), System.Text.Encoding.UTF8, "application/json")
                    );

                    // 更新按鈕狀態
                    buttonStates[button] = response.IsSuccessStatusCode ? ButtonState.Success : ButtonState.Failed;
                    buttonStateTimers[button] = DateTime.Now;
                    this.CurrentChart.RedrawBuffer();

                    // 啟動定時器來重置狀態
                    _ = Task.Delay(STATE_DISPLAY_DURATION_MS).ContinueWith(_ =>
                    {
                        if (buttonStates.ContainsKey(button))
                        {
                            buttonStates[button] = ButtonState.Normal;
                            this.CurrentChart.RedrawBuffer();
                        }
                    });
                }
            }
            catch (Exception)
            {
                buttonStates[button] = ButtonState.Failed;
                buttonStateTimers[button] = DateTime.Now;
                this.CurrentChart.RedrawBuffer();
            }
        }

        private async Task SendOrderToApix(string direction, double price,  double slPrice, double tpPrice, HoveredButton button)
        {
            try
            {
                var symbol = this.Symbol;
                var symbolId = symbol.AdditionalInfo != null && symbol.AdditionalInfo.TryGetItem(Symbol.TRADING_SYMBOL_ID, out var item) ? item.Value.ToString() : symbol.Id;
                int atIndex = symbolId.IndexOf('@');
                string tradableSymbolId = atIndex > 0 ? symbolId.Substring(0, atIndex) : symbolId;
                
                // 移除商品名稱尾部的數字
                string productName = new string(tradableSymbolId.TakeWhile(c => !char.IsDigit(c)).ToArray());

                bool isBuy = direction == "Buy";
                // 使用與懸浮提示相同的計算方法，並傳入交易方向
                string tpDiff = FormatPriceDifference(price, tpPrice, isBuy);
                string slDiff = FormatPriceDifference(price, slPrice, isBuy);

                // 從格式化的字符串中提取美金數值
                double tpDollars = double.Parse(tpDiff.Split('$')[1].Trim());
                double slDollars = double.Parse(slDiff.Split('$')[1].Trim());

                var orderData = new
                {
                    product_name = productName,
                    order_type = "Limit",
                    quantity = this.Quantity,
                    sldollars = (slDollars * this.Quantity).ToString("F2"),
                    tpdollars = (tpDollars * this.Quantity).ToString("F2"),
                    sl = slPrice.ToString(),
                    tp = tpPrice.ToString(),
                    price = price.ToString(),
                    test_mode = true,
                    direction = direction
                };

                using (var httpClient = new HttpClient())
                {
                    var response = await httpClient.PostAsync(
                        APIX_ENDPOINT,
                        new StringContent(JsonSerializer.Serialize(orderData), System.Text.Encoding.UTF8, "application/json")
                    );

                    buttonStates[button] = response.IsSuccessStatusCode ? ButtonState.Success : ButtonState.Failed;
                    buttonStateTimers[button] = DateTime.Now;
                    this.CurrentChart.RedrawBuffer();

                    _ = Task.Delay(STATE_DISPLAY_DURATION_MS).ContinueWith(_ =>
                    {
                        if (buttonStates.ContainsKey(button))
                        {
                            buttonStates[button] = ButtonState.Normal;
                            this.CurrentChart.RedrawBuffer();
                        }
                    });
                }
            }
            catch (Exception)
            {
                buttonStates[button] = ButtonState.Failed;
                buttonStateTimers[button] = DateTime.Now;
                this.CurrentChart.RedrawBuffer();
            }
        }

        // 修改按鈕繪製部分
        private string GetButtonText(HoveredButton button, string normalText)
        {
            if (buttonStates.TryGetValue(button, out ButtonState state))
            {
                switch (state)
                {
                    case ButtonState.Success:
                        return "✓";
                    case ButtonState.Failed:
                        return "✗";
                    default:
                        return normalText;
                }
            }
            return normalText;
        }

        // 添加計算VAH和VAL的方法
        private void CalculateVolumeProfile(int startIndex, int endIndex)
        {
            try
            {
                // 重置VAH、VAL、POC
                vah = 0;
                val = 0;
                poc = 0;

                if (startIndex >= endIndex || startIndex < 0 || endIndex >= this.HistoricalData.Count)
                    return;

                // 檢查Volume Analysis數據是否已加載
                if (this.HistoricalData.VolumeAnalysisCalculationProgress == null ||
                    this.HistoricalData.VolumeAnalysisCalculationProgress.State != VolumeAnalysisCalculationState.Finished)
                {
                    // 如果Volume Analysis數據未加載，使用備用方法
                    CalculateVolumeProfileBackup(startIndex, endIndex);
                    return;
                }

                Dictionary<double, double> volumeProfile = new Dictionary<double, double>();

                // 計算Volume Profile
                for (int i = startIndex; i <= endIndex; i++)
                {
                    var bar = this.HistoricalData[i, SeekOriginHistory.Begin] as HistoryItemBar;
                    if (bar == null) continue;

                    // 使用VolumeAnalysisData（最精確的方法）
                    if (bar.VolumeAnalysisData != null && bar.VolumeAnalysisData.PriceLevels != null)
                    {
                        try
                        {
                            double tickSize = Symbol.TickSize;

                            foreach (var priceLevel in bar.VolumeAnalysisData.PriceLevels)
                            {
                                double rawPrice = priceLevel.Key;
                                var volumeItem = priceLevel.Value;

                                if (volumeItem != null)
                                {
                                    // 直接使用VolumeAnalysisItem的Volume屬性
                                    double volume = volumeItem.Volume;

                                    if (volume > 0)
                                    {
                                        // 確保價格對齊到正確的tick級別
                                        double normalizedPrice = Math.Round(rawPrice / tickSize) * tickSize;

                                        if (volumeProfile.ContainsKey(normalizedPrice))
                                            volumeProfile[normalizedPrice] += volume;
                                        else
                                            volumeProfile[normalizedPrice] = volume;
                                    }
                                }
                            }
                        }
                        catch
                        {
                            // 如果VolumeAnalysisData處理失敗，使用備用方法
                            UseBackupVolumeCalculation(bar, volumeProfile);
                        }
                    }
                    else
                    {
                        // 如果沒有VolumeAnalysisData，使用備用方法
                        UseBackupVolumeCalculation(bar, volumeProfile);
                    }
                }

                if (volumeProfile.Count == 0) return;

                // 找到POC（成交量最大的價格）
                var pocEntry = volumeProfile.OrderByDescending(v => v.Value).First();
                poc = pocEntry.Key;

                // 計算總成交量
                double totalVolume = volumeProfile.Values.Sum();
                double valueAreaThreshold = totalVolume * 0.70; // 70%的成交量

                // 使用導師建議的方法計算Value Area
                CalculateValueAreaFromPOC(volumeProfile, valueAreaThreshold);
            }
            catch (Exception ex)
            {
                // 如果計算失敗，重置為0
                vah = 0;
                val = 0;
                poc = 0;
            }
        }

        // 備用的Volume計算方法
        private void UseBackupVolumeCalculation(HistoryItemBar bar, Dictionary<double, double> volumeProfile)
        {
            double tickSize = Symbol.TickSize;
            if (tickSize <= 0) tickSize = 0.01;

            int maxLevels = 50;
            double priceRange = bar.High - bar.Low;
            if (priceRange <= 0) return;

            int priceLevels = Math.Min(maxLevels, Math.Max(1, (int)Math.Ceiling(priceRange / tickSize)));
            double adjustedTickSize = priceRange / priceLevels;

            double closePrice = bar.Close;
            double openPrice = bar.Open;

            for (int level = 0; level < priceLevels; level++)
            {
                double currentPrice = bar.Low + level * adjustedTickSize;
                double distanceToClose = Math.Abs(currentPrice - closePrice) / priceRange;
                double distanceToOpen = Math.Abs(currentPrice - openPrice) / priceRange;

                double closeWeight = Math.Exp(-Math.Pow(distanceToClose * 2.5, 2));
                double openWeight = Math.Exp(-Math.Pow(distanceToOpen * 3, 2)) * 0.5;
                double baseWeight = 0.1;
                double totalWeight = closeWeight + openWeight + baseWeight;

                double volumeAtLevel = bar.Volume * totalWeight / priceLevels;
                double roundedPrice = Math.Round(currentPrice / tickSize) * tickSize;

                if (volumeProfile.ContainsKey(roundedPrice))
                    volumeProfile[roundedPrice] += volumeAtLevel;
                else
                    volumeProfile[roundedPrice] = volumeAtLevel;
            }
        }

        // 根據導師建議的方法從POC開始計算Value Area
        private void CalculateValueAreaFromPOC(Dictionary<double, double> volumeProfile, double valueAreaThreshold)
        {
            if (volumeProfile.Count == 0 || poc == 0) return;

            double accumulatedVolume = volumeProfile.ContainsKey(poc) ? volumeProfile[poc] : 0;
            double tickSize = Symbol.TickSize;

            // 確保tickSize有效
            if (tickSize <= 0)
            {
                // 如果tickSize無效，嘗試從價格數據推斷最小價格間隔
                var sortedPrices = volumeProfile.Keys.OrderBy(p => p).ToList();
                if (sortedPrices.Count > 1)
                {
                    double minDiff = double.MaxValue;
                    for (int i = 1; i < sortedPrices.Count; i++)
                    {
                        double diff = sortedPrices[i] - sortedPrices[i - 1];
                        if (diff > 0 && diff < minDiff)
                            minDiff = diff;
                    }
                    tickSize = minDiff > 0 ? minDiff : 0.01;
                }
                else
                {
                    tickSize = 0.01;
                }
            }

            double currentHigh = poc;
            double currentLow = poc;

            // 從POC開始，向上下擴展直到達到70%的成交量
            while (accumulatedVolume < valueAreaThreshold)
            {
                double volumeAbove = 0;
                double volumeBelow = 0;

                // 檢查上方一個tick的成交量，使用精確的價格對齊
                double priceAbove = Math.Round((currentHigh + tickSize) / tickSize) * tickSize;
                if (volumeProfile.ContainsKey(priceAbove))
                    volumeAbove = volumeProfile[priceAbove];

                // 檢查下方一個tick的成交量，使用精確的價格對齊
                double priceBelow = Math.Round((currentLow - tickSize) / tickSize) * tickSize;
                if (volumeProfile.ContainsKey(priceBelow))
                    volumeBelow = volumeProfile[priceBelow];

                // 選擇成交量較大的方向擴展
                if (volumeAbove >= volumeBelow && volumeAbove > 0)
                {
                    currentHigh = priceAbove;
                    accumulatedVolume += volumeAbove;
                }
                else if (volumeBelow > 0)
                {
                    currentLow = priceBelow;
                    accumulatedVolume += volumeBelow;
                }
                else
                {
                    // 如果兩個方向都沒有成交量，停止擴展
                    break;
                }

                // 防止無限循環的安全檢查
                if (currentHigh - currentLow > 1000 * tickSize)
                {
                    break;
                }
            }

            vah = currentHigh;
            val = currentLow;
        }

        // 備用的Volume Profile計算方法（當Volume Analysis數據未加載時使用）
        private void CalculateVolumeProfileBackup(int startIndex, int endIndex)
        {
            try
            {
                Dictionary<double, double> volumeProfile = new Dictionary<double, double>();

                // 使用簡化的方法計算Volume Profile
                for (int i = startIndex; i <= endIndex; i++)
                {
                    var bar = this.HistoricalData[i, SeekOriginHistory.Begin] as HistoryItemBar;
                    if (bar == null) continue;

                    UseBackupVolumeCalculation(bar, volumeProfile);
                }

                if (volumeProfile.Count == 0) return;

                // 找到POC（成交量最大的價格）
                var pocEntry = volumeProfile.OrderByDescending(v => v.Value).First();
                poc = pocEntry.Key;

                // 計算總成交量
                double totalVolume = volumeProfile.Values.Sum();
                double valueAreaThreshold = totalVolume * 0.70; // 70%的成交量

                // 計算Value Area
                CalculateValueAreaFromPOC(volumeProfile, valueAreaThreshold);
            }
            catch (Exception ex)
            {
                // 如果計算失敗，重置為0
                vah = 0;
                val = 0;
                poc = 0;
            }
        }

        // 添加計算ATR的方法
        private double CalculateATR(int startIndex, int endIndex)
        {
            if (startIndex >= endIndex) return 0;

            List<double> trueRanges = new List<double>();
            double multiplier = 1.0; // ATR倍數，可以根據需要調整

            // 向前和向後多取K線來計算更準確的ATR
            int extendedStartIndex = Math.Max(0, startIndex - 60); // 向前多取60根K線
            int extendedEndIndex = Math.Min(this.HistoricalData.Count - 1, endIndex + 60); // 向後多取60根K線，但不超過數據範圍


            // 計算每根K線的TR
            for (int i = extendedStartIndex; i <= extendedEndIndex; i++)
            {
                if (i == 0) continue;

                var currentBar = this.HistoricalData[i, SeekOriginHistory.Begin] as HistoryItemBar;
                var previousBar = this.HistoricalData[i - 1, SeekOriginHistory.Begin] as HistoryItemBar;

                if (currentBar == null || previousBar == null) continue;

                // 計算三種範圍
                double tr1 = Math.Abs(currentBar.High - currentBar.Low);
                double tr2 = Math.Abs(currentBar.High - previousBar.Close);
                double tr3 = Math.Abs(currentBar.Low - previousBar.Close);

                // TR是三者中的最大值
                double tr = Math.Max(tr1, Math.Max(tr2, tr3));
                trueRanges.Add(tr);
            }

            // 計算平均值並乘以倍數
            return trueRanges.Count > 0 ? trueRanges.Average() * multiplier : 0;
        }

        private double GetPercentATR()
        {
            var mainWindow = this.CurrentChart.MainWindow;
            
            // 獲取可見區域的時間範圍
            DateTime startTime = mainWindow.CoordinatesConverter.GetTime(mainWindow.ClientRectangle.Left);
            DateTime endTime = mainWindow.CoordinatesConverter.GetTime(mainWindow.ClientRectangle.Right);
            DateTime currentTime = HistoricalData[HistoricalData.Count - 1, SeekOriginHistory.Begin].TimeLeft;
            
            if (endTime > currentTime)
                endTime = currentTime;
            
            // 計算畫面中間80%的範圍
            int visibleBars = (int)(mainWindow.CoordinatesConverter.GetBarIndex(endTime) - mainWindow.CoordinatesConverter.GetBarIndex(startTime));
            int middle80PercentBars = (int)(visibleBars * 0.618);
            int offset = (visibleBars - middle80PercentBars) / 2;

            int startIndex = (int)mainWindow.CoordinatesConverter.GetBarIndex(startTime) + offset;
            int endIndex = (int)mainWindow.CoordinatesConverter.GetBarIndex(endTime) - offset;

            // 確保索引在有效範圍內
            startIndex = Math.Max(0, startIndex);
            endIndex = Math.Min(endIndex, HistoricalData.Count - 1);

            // 使用CalculateATR計算當前可見範圍的ATR
            return CalculateATR(startIndex, endIndex);
        }

        // 測試Volume Analysis數據可用性的方法
        private bool IsVolumeAnalysisDataAvailable()
        {
            if (this.HistoricalData.VolumeAnalysisCalculationProgress == null)
                return false;

            return this.HistoricalData.VolumeAnalysisCalculationProgress.State == VolumeAnalysisCalculationState.Finished;
        }

        // 獲取Volume Analysis數據統計信息的方法（用於調試）
        private string GetVolumeAnalysisDebugInfo(int startIndex, int endIndex)
        {
            if (!IsVolumeAnalysisDataAvailable())
                return "Volume Analysis data not available";

            int barsWithVolumeData = 0;
            int totalPriceLevels = 0;
            double minPrice = double.MaxValue;
            double maxPrice = double.MinValue;
            double tickSize = Symbol.TickSize;

            for (int i = startIndex; i <= endIndex && i < this.HistoricalData.Count; i++)
            {
                var bar = this.HistoricalData[i, SeekOriginHistory.Begin] as HistoryItemBar;
                if (bar?.VolumeAnalysisData?.PriceLevels != null)
                {
                    barsWithVolumeData++;
                    totalPriceLevels += bar.VolumeAnalysisData.PriceLevels.Count;

                    // 收集價格範圍信息
                    foreach (var priceLevel in bar.VolumeAnalysisData.PriceLevels)
                    {
                        double price = priceLevel.Key;
                        minPrice = Math.Min(minPrice, price);
                        maxPrice = Math.Max(maxPrice, price);
                    }
                }
            }

            double priceRange = maxPrice - minPrice;
            int expectedTicks = priceRange > 0 ? (int)(priceRange / tickSize) : 0;

            return $"Bars: {barsWithVolumeData}/{endIndex - startIndex + 1}, Levels: {totalPriceLevels}, " +
                   $"TickSize: {tickSize}, Range: {priceRange:F4}, ExpectedTicks: {expectedTicks}";
        }

        // 獲取商品特定的價格精度信息
        private string GetSymbolPrecisionInfo()
        {
            double tickSize = Symbol.TickSize;
            string symbolName = Symbol.Name ?? "Unknown";

            // 計算小數位數
            int decimalPlaces = 0;
            if (tickSize > 0)
            {
                string tickStr = tickSize.ToString("F10");
                int dotIndex = tickStr.IndexOf('.');
                if (dotIndex >= 0)
                {
                    decimalPlaces = tickStr.TrimEnd('0').Length - dotIndex - 1;
                }
            }

            return $"Symbol: {symbolName}, TickSize: {tickSize}, DecimalPlaces: {decimalPlaces}";
        }

        // 標準化價格到正確的tick級別
        private double NormalizePrice(double price)
        {
            double tickSize = Symbol.TickSize;
            if (tickSize <= 0) return price;

            return Math.Round(price / tickSize) * tickSize;
        }

        // 選擇輪相關方法
        private int GetWheelOptionAtPoint(Point point)
        {
            if (!showFiboWheel) return -1;

            // 計算點到輪心的距離
            double dx = point.X - wheelCenter.X;
            double dy = point.Y - wheelCenter.Y;
            double distance = Math.Sqrt(dx * dx + dy * dy);

            // 如果在內圓範圍內，不返回任何選項（只能提起Exit）
            if (distance < 20) return -1;

            // 計算角度（從上方開始，順時針）
            double angle = Math.Atan2(dx, -dy);
            if (angle < 0) angle += 2 * Math.PI;

            // 將角度轉換為選項索引（4個選項，每個90度）
            // 0: FiboType 0 (上方，0-90度)
            // 1: FiboType 1 (右方，90-180度)
            // 2: FiboType 2 (下方，180-270度)
            // 3: Exit (左方，270-360度)
            int option = (int)(angle / (Math.PI / 2));
            return option % 4;
        }

        private void HandleWheelSelection(int option)
        {
            switch (option)
            {
                case 0:
                    FiboType = 0;
                    break;
                case 1:
                    FiboType = 1;
                    break;
                case 2:
                    FiboType = 2;
                    break;
                case 3:
                    // Exit - 重置所有狀態
                    ResetAllStates();
                    break;
            }
        }

        private void DrawFiboWheel(Graphics gr)
        {
            if (!showFiboWheel) return;

            // 繪製外圓 - 調整為半透明效果
            using (var outerBrush = new SolidBrush(Color.FromArgb(120, Color.DarkGray))) // 降低透明度
            using (var hoverBrush = new SolidBrush(Color.FromArgb(150, Color.Orange))) // 降低透明度
            using (var textBrush = new SolidBrush(Color.FromArgb(220, Color.White))) // 文字稍微透明
            using (var font = new Font("Arial", 10, FontStyle.Bold))
            {
                // 創建環形路徑（外圓減去內圓）
                using (var wheelPath = new System.Drawing.Drawing2D.GraphicsPath())
                {
                    Rectangle outerRect = new Rectangle(
                        wheelCenter.X - wheelRadius,
                        wheelCenter.Y - wheelRadius,
                        wheelRadius * 2,
                        wheelRadius * 2
                    );

                    int innerRadius = 20;
                    Rectangle innerRect = new Rectangle(
                        wheelCenter.X - innerRadius,
                        wheelCenter.Y - innerRadius,
                        innerRadius * 2,
                        innerRadius * 2
                    );

                    // 添加外圓和內圓，使用Alternate模式創建環形
                    wheelPath.AddEllipse(outerRect);
                    wheelPath.AddEllipse(innerRect);
                    wheelPath.FillMode = System.Drawing.Drawing2D.FillMode.Alternate;

                    // 繪製環形背景
                    gr.FillPath(outerBrush, wheelPath);
                }

                // 繪製4個選項區域
                for (int i = 0; i < 4; i++)
                {
                    float startAngle = i * 90 - 90; // 從-90度開始，讓第一個選項在上方

                    // 如果是懸停的選項，使用高亮顏色（只在環形區域，不包括內圓）
                    if (i == hoveredWheelOption)
                    {
                        using (var path = new System.Drawing.Drawing2D.GraphicsPath())
                        {
                            Rectangle outerRect = new Rectangle(
                                wheelCenter.X - wheelRadius,
                                wheelCenter.Y - wheelRadius,
                                wheelRadius * 2,
                                wheelRadius * 2
                            );

                            int hoverInnerRadius = 20;
                            Rectangle hoverInnerRect = new Rectangle(
                                wheelCenter.X - hoverInnerRadius,
                                wheelCenter.Y - hoverInnerRadius,
                                hoverInnerRadius * 2,
                                hoverInnerRadius * 2
                            );

                            // 創建環形扇形：先添加外圓扇形，再減去內圓扇形
                            path.AddPie(outerRect, startAngle, 90);
                            path.AddPie(hoverInnerRect, startAngle, 90);

                            // 設置填充模式為交替，這樣內圓扇形會被"挖空"
                            path.FillMode = System.Drawing.Drawing2D.FillMode.Alternate;
                            gr.FillPath(hoverBrush, path);
                        }
                    }

                    // 計算文字位置
                    double angle = (startAngle + 45) * Math.PI / 180; // 轉換為弧度，取中間角度
                    float textRadius = wheelRadius * 0.7f;
                    float textX = wheelCenter.X + (float)(Math.Cos(angle) * textRadius);
                    float textY = wheelCenter.Y + (float)(Math.Sin(angle) * textRadius);

                    // 繪製文字
                    using (var format = new StringFormat())
                    {
                        format.Alignment = StringAlignment.Center;
                        format.LineAlignment = StringAlignment.Center;
                        gr.DrawString(wheelOptions[i], font, textBrush, textX, textY, format);
                    }
                }

                // 繪製分割線 - 半透明效果
                using (var pen = new Pen(Color.FromArgb(180, Color.White), 2))
                {
                    int innerRadius = 20; // 重新定義innerRadius
                    for (int i = 0; i < 4; i++)
                    {
                        double angle = (i * 90 - 90) * Math.PI / 180;
                        float x1 = wheelCenter.X + (float)(Math.Cos(angle) * innerRadius);
                        float y1 = wheelCenter.Y + (float)(Math.Sin(angle) * innerRadius);
                        float x2 = wheelCenter.X + (float)(Math.Cos(angle) * wheelRadius);
                        float y2 = wheelCenter.Y + (float)(Math.Sin(angle) * wheelRadius);
                        gr.DrawLine(pen, x1, y1, x2, y2);
                    }
                }
            }
        }
    }

    

    // Helper class to store both English and Chinese names for Tarot cards
    public class TarotCard
    {
        public string EnglishName { get; set; }
        public string ChineseName { get; set; }
        public string EncryptedEnglishName { get; set; }
        public string EncryptedChineseName { get; set; }

        public TarotCard(string englishName, string chineseName)
        {
            EnglishName = englishName;
            ChineseName = chineseName;
        }
    }

    // Define the expected API response format
    public class ApiResponse
    {
        public string Interpretation { get; set; }
        //You can add more properties here based on your actual API response format
    }




}