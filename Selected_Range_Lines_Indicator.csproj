<?xml version="1.0" encoding="utf-8"?>
<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net8</TargetFramework>
    <LangVersion>latest</LangVersion>
    <AppendTargetFrameworkToOutputPath>false</AppendTargetFrameworkToOutputPath>
    <Platforms>AnyCPU</Platforms>
    <AlgoType>Indicator</AlgoType>
    <AssemblyName>Selected_Range_Lines_Indicator</AssemblyName>
    <RootNamespace>Selected_Range_Lines_Indicator</RootNamespace>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <OutputPath>D:\Quantower\TradingPlatform\v1.144.2\..\..\Settings\Scripts\Indicators\Selected_Range_Lines_Indicator</OutputPath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <OutputPath>D:\Quantower\TradingPlatform\v1.144.2\..\..\Settings\Scripts\Indicators\Selected_Range_Lines_Indicator</OutputPath>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="System.Drawing.Common" Version="9.0.0" />
  </ItemGroup>
  <ItemGroup>
    <Reference Include="TradingPlatform.BusinessLayer">
      <HintPath>D:\Quantower\TradingPlatform\v1.144.2\bin\TradingPlatform.BusinessLayer.dll</HintPath>
      <Private>False</Private>
    </Reference>
  </ItemGroup>

  <ItemGroup>
    <Content Include="tarots\**\*.*">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
  </ItemGroup>

  <!-- 複製tarots文件夾到LocalStorages目錄 -->
  <Target Name="CopyTarotsToLocalStorages" AfterTargets="Build">
    <ItemGroup>
      <TarotFiles Include="tarots\**\*.*" />
    </ItemGroup>
    <Copy SourceFiles="@(TarotFiles)"
          DestinationFiles="D:\Quantower\TradingPlatform\v1.144.2\bin\LocalStorages\Indicators\SelectedRangeLines\tarots\%(RecursiveDir)%(Filename)%(Extension)"
          SkipUnchangedFiles="true" />
  </Target>
</Project>