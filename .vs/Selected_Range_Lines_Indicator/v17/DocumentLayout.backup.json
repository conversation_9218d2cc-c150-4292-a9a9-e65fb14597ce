{"Version": 1, "WorkspaceRootPath": "C:\\Users\\<USER>\\source\\repos\\Selected_Range_Lines_Indicator\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{29B3B56B-491B-4299-AC62-13B9C35D07FC}|Selected_Range_Lines_Indicator.csproj|c:\\users\\<USER>\\source\\repos\\selected_range_lines_indicator\\selected_range_lines_indicator.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{29B3B56B-491B-4299-AC62-13B9C35D07FC}|Selected_Range_Lines_Indicator.csproj|solutionrelative:selected_range_lines_indicator.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 0, "Children": [{"$type": "Document", "DocumentIndex": 0, "Title": "Selected_Range_Lines_Indicator.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\Selected_Range_Lines_Indicator\\Selected_Range_Lines_Indicator.cs", "RelativeDocumentMoniker": "Selected_Range_Lines_Indicator.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\Selected_Range_Lines_Indicator\\Selected_Range_Lines_Indicator.cs", "RelativeToolTip": "Selected_Range_Lines_Indicator.cs", "ViewState": "AgIAAJcEAAAAAAAAAAAhwKcEAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-01-04T08:13:35.809Z", "EditorCaption": ""}]}]}]}